{"name": "next-supabase-saas-kit-turbo", "private": true, "sideEffects": false, "engines": {"node": ">=v18.18.0"}, "author": "MakerKit (https://makerkit.dev)", "scripts": {"preinstall": "pnpm run --filter scripts requirements", "postinstall": "manypkg fix", "build": "turbo build --cache-dir=.turbo", "clean": "git clean -xdf node_modules dist .next", "clean:workspaces": "turbo clean", "dev": "cross-env FORCE_COLOR=1 turbo dev --parallel", "format": "turbo format --cache-dir=.turbo --continue -- --cache --cache-location='node_modules/.cache/.prettiercache' --ignore-path='../../.gitignore'", "format:fix": "turbo format --cache-dir=.turbo --continue -- --write --cache --cache-location='node_modules/.cache/.prettiercache' --ignore-path='../../.gitignore'", "lint": "turbo lint --cache-dir=.turbo --continue -- --cache --cache-location 'node_modules/.cache/.eslintcache' && manypkg check", "lint:fix": "turbo lint --cache-dir=.turbo --continue -- --fix --cache --cache-location 'node_modules/.cache/.eslintcache' && manypkg fix", "typecheck": "turbo typecheck --cache-dir=.turbo", "test": "turbo test --cache-dir=.turbo", "update": "pnpm update -r", "syncpack:list": "pnpm dlx syncpack list-mismatches", "syncpack:fix": "pnpm dlx syncpack fix-mismatches", "supabase:web:start": "pnpm --filter web supabase:start", "supabase:web:stop": "pnpm --filter web supabase:stop", "supabase:web:typegen": "pnpm --filter web supabase:typegen", "supabase:web:reset": "pnpm --filter web supabase:reset", "stripe:listen": "pnpm --filter '@kit/stripe' start", "env:generate": "turbo gen env", "env:validate": "turbo gen validate-env"}, "prettier": "@kit/prettier-config", "packageManager": "pnpm@9.12.0", "pnpm": {"overrides": {"react-is": "rc"}}, "devDependencies": {"@manypkg/cli": "^0.23.0", "@supabase/supabase-js": "^2.47.3", "@turbo/gen": "^2.3.3", "ag-charts-community": "^11.3.1", "ag-charts-react": "^11.3.1", "ag-grid-enterprise": "^33.0.4", "cross-env": "^7.0.3", "dotenv-cli": "^7.4.4", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-config-turbo": "^2.3.3", "eslint-import-resolver-node": "^0.3.9", "eslint-module-utils": "^2.12.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-turbo": "^2.3.3", "eslint-scope": "^7.2.2", "eslint-visitor-keys": "^3.4.3", "node-html-parser": "^7.0.1", "pino-pretty": "^13.0.0", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "react-icons": "^5.4.0", "recharts": "2.14.1", "turbo": "2.3.3", "typescript": "^5.7.2"}, "description": "This is a Starter Kit for building SaaS applications using Supabase, Next.js, and Tailwind CSS.", "version": "1.0.0", "main": "index.js", "keywords": [], "license": "ISC"}