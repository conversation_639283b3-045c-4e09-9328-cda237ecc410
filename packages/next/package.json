{"name": "@kit/next", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "exports": {"./actions": "./src/actions/index.ts", "./routes": "./src/routes/index.ts"}, "devDependencies": {"@kit/auth": "workspace:*", "@kit/eslint-config": "workspace:*", "@kit/monitoring": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/supabase": "workspace:*", "@kit/tailwind-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@supabase/supabase-js": "^2.47.3", "next": "15.1.0", "zod": "^3.24.1"}, "eslintConfig": {"root": true, "extends": ["@kit/eslint-config/base", "@kit/eslint-config/react"]}, "typesVersions": {"*": {"*": ["src/*"]}}}