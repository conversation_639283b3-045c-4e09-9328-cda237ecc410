{"name": "@kit/i18n", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "exports": {".": "./src/index.ts", "./server": "./src/i18n.server.ts", "./client": "./src/i18n.client.ts", "./provider": "./src/i18n-provider.tsx"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/shared": "workspace:*", "@kit/tailwind-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@tanstack/react-query": "5.62.7", "next": "15.1.0", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.1.4"}, "dependencies": {"i18next": "24.0.5", "i18next-browser-languagedetector": "8.0.2", "i18next-resources-to-backend": "^1.2.1"}, "eslintConfig": {"root": true, "extends": ["@kit/eslint-config/base", "@kit/eslint-config/react"]}, "typesVersions": {"*": {"*": ["src/*"]}}}