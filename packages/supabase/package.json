{"name": "@kit/supabase", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "exports": {"./server-client": "./src/clients/server-client.ts", "./server-admin-client": "./src/clients/server-admin-client.ts", "./middleware-client": "./src/clients/middleware-client.ts", "./server-actions-client": "./src/clients/server-actions-client.ts", "./route-handler-client": "./src/clients/route-handler-client.ts", "./server-component-client": "./src/clients/server-component-client.ts", "./browser-client": "./src/clients/browser-client.ts", "./check-requires-mfa": "./src/check-requires-mfa.ts", "./require-user": "./src/require-user.ts", "./hooks/*": "./src/hooks/*.ts", "./database": "./src/database.types.ts", "./auth": "./src/auth.ts"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tailwind-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.47.3", "@tanstack/react-query": "5.62.7", "@types/react": "npm:types-react@19.0.0-rc.1", "next": "15.1.0", "react": "19.0.0", "server-only": "^0.0.1", "zod": "^3.24.1"}, "eslintConfig": {"root": true, "extends": ["@kit/eslint-config/base", "@kit/eslint-config/react"]}, "typesVersions": {"*": {"*": ["src/*"]}}}