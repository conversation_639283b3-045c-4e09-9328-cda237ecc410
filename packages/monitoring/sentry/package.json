{"name": "@kit/sentry", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "exports": {".": "./src/index.ts", "./provider": "./src/components/provider.tsx", "./config/client": "./src/sentry.client.config.ts", "./config/server": "./src/sentry.client.server.ts"}, "dependencies": {"@sentry/nextjs": "^8.43.0"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/monitoring-core": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tailwind-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@types/react": "npm:types-react@19.0.0-rc.1", "react": "19.0.0"}, "eslintConfig": {"root": true, "extends": ["@kit/eslint-config/base", "@kit/eslint-config/react"]}, "typesVersions": {"*": {"*": ["src/*"]}}}