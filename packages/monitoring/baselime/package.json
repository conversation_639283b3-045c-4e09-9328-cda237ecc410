{"name": "@kit/baselime", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "exports": {"./server": "./src/server.ts", "./client": "./src/client.ts", "./instrumentation": "./src/instrumentation.ts", "./provider": "./src/components/provider.tsx"}, "dependencies": {"@baselime/node-opentelemetry": "^0.5.8", "@baselime/react-rum": "^0.3.1", "@kit/monitoring-core": "workspace:*"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tailwind-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@types/react": "npm:types-react@19.0.0-rc.1", "react": "19.0.0", "zod": "^3.24.1"}, "eslintConfig": {"root": true, "extends": ["@kit/eslint-config/base", "@kit/eslint-config/react"]}, "typesVersions": {"*": {"*": ["src/*"]}}}