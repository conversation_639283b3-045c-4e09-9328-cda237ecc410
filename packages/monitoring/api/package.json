{"name": "@kit/monitoring", "private": true, "sideEffects": false, "version": "0.1.0", "scripts": {"clean": "git clean -xdf ../.turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint ..", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "exports": {"./server": "./src/server.ts", "./instrumentation": "./src/instrumentation.ts", "./hooks": "./src/hooks/index.ts", "./components": "./src/components/index.ts"}, "devDependencies": {"@kit/baselime": "workspace:*", "@kit/eslint-config": "workspace:*", "@kit/monitoring-core": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/sentry": "workspace:*", "@kit/tailwind-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@types/react": "npm:types-react@19.0.0-rc.1", "react": "19.0.0"}, "eslintConfig": {"root": true, "extends": ["@kit/eslint-config/base", "@kit/eslint-config/react"]}, "typesVersions": {"*": {"*": ["src/*"]}}}