{"name": "@kit/billing-gateway", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "exports": {".": "./src/index.ts", "./components": "./src/components/index.ts", "./checkout": "./src/components/embedded-checkout.tsx", "./marketing": "./src/components/marketing.tsx"}, "devDependencies": {"@hookform/resolvers": "^3.9.1", "@kit/billing": "workspace:*", "@kit/eslint-config": "workspace:*", "@kit/lemon-squeezy": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/shared": "workspace:*", "@kit/stripe": "workspace:*", "@kit/supabase": "workspace:*", "@kit/tailwind-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@kit/ui": "workspace:*", "@supabase/supabase-js": "^2.47.3", "@types/react": "npm:types-react@19.0.0-rc.1", "date-fns": "^4.1.0", "lucide-react": "^0.468.0", "next": "15.1.0", "react": "19.0.0", "react-hook-form": "^7.54.0", "react-i18next": "^15.1.4", "zod": "^3.24.1"}, "eslintConfig": {"root": true, "extends": ["@kit/eslint-config/base", "@kit/eslint-config/react"]}, "typesVersions": {"*": {"*": ["src/*"]}}}