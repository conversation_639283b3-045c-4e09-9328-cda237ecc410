import React from 'react';

import { cn } from '../../lib/utils';
import { HeroTitle } from './hero-title';

interface HeroProps {
  pill?: React.ReactNode;
  title: React.ReactNode;
  subtitle?: React.ReactNode;
  cta?: React.ReactNode;
  image?: React.ReactNode;
  className?: string;
  animate?: boolean;
}

export function Hero({
  pill,
  title,
  subtitle,
  cta,
  image,
  className,
  animate = true,
}: HeroP<PERSON>) {
  return (
    <div className={cn('mx-auto flex flex-col space-y-20', className)}>
      <div
        style={{
          MozAnimationDuration: '100ms',
        }}
        className={cn(
          'mx-auto flex flex-1 flex-col items-center justify-center duration-1000 md:flex-row',
          {
            ['animate-in fade-in zoom-in-90 slide-in-from-top-36']: animate,
          },
        )}
      >
        <div className="flex w-full flex-1 flex-col items-center space-y-6 xl:space-y-8 2xl:space-y-10">
          {pill && (
            <div
              className={cn({
                ['delay-300 duration-700 animate-in fade-in fill-mode-both']:
                  animate,
              })}
            >
              {pill}
            </div>
          )}

          <div className="flex flex-col items-center space-y-8">
            <HeroTitle>{title}</HeroTitle>

            {subtitle && (
              <div className="flex max-w-2xl flex-col space-y-1">
                <h3 className="p-0 text-center font-sans text-xl font-normal tracking-tight text-muted-foreground">
                  {subtitle}
                </h3>
              </div>
            )}
          </div>

          {cta && (
            <div
              className={cn({
                ['delay-500 duration-1000 animate-in fade-in fill-mode-both']:
                  animate,
              })}
            >
              {cta}
            </div>
          )}
        </div>
      </div>

      {image && (
        <div
          style={{
            MozAnimationDuration: '100ms',
          }}
          className={cn('container mx-auto flex justify-center py-8', {
            ['delay-1000 duration-1000 animate-in fade-in zoom-in-95 slide-in-from-top-32 fill-mode-both']:
              animate,
          })}
        >
          {image}
        </div>
      )}
    </div>
  );
}
