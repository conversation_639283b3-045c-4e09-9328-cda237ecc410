{"name": "@kit/auth", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "exports": {"./sign-in": "./src/sign-in.ts", "./sign-up": "./src/sign-up.ts", "./password-reset": "./src/password-reset.ts", "./shared": "./src/shared.ts", "./mfa": "./src/mfa.ts", "./captcha/client": "./src/captcha/client/index.ts", "./captcha/server": "./src/captcha/server/index.ts", "./resend-email-link": "./src/components/resend-auth-link-form.tsx"}, "devDependencies": {"@hookform/resolvers": "^3.9.1", "@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/shared": "workspace:*", "@kit/supabase": "workspace:*", "@kit/tailwind-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@kit/ui": "workspace:*", "@marsidev/react-turnstile": "^1.1.0", "@radix-ui/react-icons": "^1.3.2", "@supabase/supabase-js": "^2.47.3", "@tanstack/react-query": "5.62.7", "@types/react": "npm:types-react@19.0.0-rc.1", "lucide-react": "^0.468.0", "next": "15.1.0", "react-hook-form": "^7.54.0", "react-i18next": "^15.1.4", "sonner": "^1.7.1", "zod": "^3.24.1"}, "prettier": "@kit/prettier-config", "eslintConfig": {"root": true, "extends": ["@kit/eslint-config/base", "@kit/eslint-config/react"]}, "typesVersions": {"*": {"*": ["src/*"]}}}