{"name": "@kit/admin", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "devDependencies": {"@hookform/resolvers": "^3.9.1", "@kit/eslint-config": "workspace:*", "@kit/next": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/shared": "workspace:*", "@kit/supabase": "workspace:*", "@kit/tailwind-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@kit/ui": "workspace:*", "@makerkit/data-loader-supabase-core": "^0.0.8", "@makerkit/data-loader-supabase-nextjs": "^1.2.3", "@supabase/supabase-js": "^2.47.3", "@tanstack/react-query": "5.62.7", "@tanstack/react-table": "^8.20.5", "@types/react": "npm:types-react@19.0.0-rc.1", "lucide-react": "^0.468.0", "next": "15.1.0", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.54.0", "zod": "^3.24.1"}, "exports": {".": "./src/index.ts", "./components/*": "./src/components/*.tsx"}, "eslintConfig": {"root": true, "extends": ["@kit/eslint-config/base", "@kit/eslint-config/react"]}, "typesVersions": {"*": {"*": ["src/*"]}}}