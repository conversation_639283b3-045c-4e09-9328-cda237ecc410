{"name": "@kit/team-accounts", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "exports": {"./api": "./src/server/api.ts", "./components": "./src/components/index.ts", "./hooks/*": "./src/hooks/*.ts", "./webhooks": "./src/server/services/webhooks/index.ts"}, "dependencies": {"nanoid": "^5.0.9"}, "devDependencies": {"@hookform/resolvers": "^3.9.1", "@kit/accounts": "workspace:*", "@kit/billing-gateway": "workspace:*", "@kit/email-templates": "workspace:*", "@kit/eslint-config": "workspace:*", "@kit/mailers": "workspace:*", "@kit/monitoring": "workspace:*", "@kit/next": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/shared": "workspace:*", "@kit/supabase": "workspace:*", "@kit/tailwind-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@kit/ui": "workspace:*", "@supabase/supabase-js": "^2.47.3", "@tanstack/react-query": "5.62.7", "@tanstack/react-table": "^8.20.5", "@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1", "class-variance-authority": "^0.7.1", "date-fns": "^4.1.0", "lucide-react": "^0.468.0", "next": "15.1.0", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.54.0", "react-i18next": "^15.1.4", "sonner": "^1.7.1", "zod": "^3.24.1"}, "prettier": "@kit/prettier-config", "eslintConfig": {"root": true, "extends": ["@kit/eslint-config/base", "@kit/eslint-config/react"]}, "typesVersions": {"*": {"*": ["src/*"]}}}