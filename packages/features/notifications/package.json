{"name": "@kit/notifications", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "exports": {"./api": "./src/server/api.ts", "./components": "./src/components/index.ts", "./hooks": "./src/hooks/index.ts"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/supabase": "workspace:*", "@kit/tailwind-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@kit/ui": "workspace:*", "@supabase/supabase-js": "^2.47.3", "@tanstack/react-query": "5.62.7", "@types/react": "npm:types-react@19.0.0-rc.1", "lucide-react": "^0.468.0", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.1.4"}, "prettier": "@kit/prettier-config", "eslintConfig": {"root": true, "extends": ["@kit/eslint-config/base", "@kit/eslint-config/react"]}, "typesVersions": {"*": {"*": ["src/*"]}}}