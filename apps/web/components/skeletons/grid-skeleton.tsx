import { <PERSON>, CardContent, CardHeader } from '@kit/ui/card';
import { Skeleton } from '~/components/ui/skeleton';

/**
 * Skeleton for AG Grid table rows
 */
export function TableRowSkeleton() {
  return (
    <div className="flex items-center space-x-4 p-4 border-b border-gray-100">
      <Skeleton className="h-4 w-32" />
      <Skeleton className="h-4 w-24" />
      <Skeleton className="h-4 w-16" />
      <Skeleton className="h-4 w-20" />
      <Skeleton className="h-4 w-28" />
    </div>
  );
}

/**
 * Skeleton for AG Grid header
 */
export function TableHeaderSkeleton() {
  return (
    <div className="flex items-center space-x-4 p-4 border-b-2 border-gray-200 bg-gray-50">
      <Skeleton className="h-4 w-24" />
      <Skeleton className="h-4 w-20" />
      <Skeleton className="h-4 w-16" />
      <Skeleton className="h-4 w-18" />
      <Skeleton className="h-4 w-22" />
    </div>
  );
}

/**
 * Complete AG Grid skeleton
 */
export function AGGridSkeleton({ rows = 10 }: { rows?: number }) {
  return (
    <Card className="w-full">
      <CardContent className="p-0">
        <div className="ag-theme-quartz" style={{ height: '500px', width: '100%' }}>
          <TableHeaderSkeleton />
          {Array.from({ length: rows }).map((_, index) => (
            <TableRowSkeleton key={index} />
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Skeleton for competitors grid
 */
export function CompetitorsGridSkeleton() {
  return (
    <div className="space-y-4">
      {/* Search and filters skeleton */}
      <div className="flex items-center justify-between">
        <Skeleton className="h-10 w-64" />
        <div className="flex space-x-2">
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-10 w-24" />
        </div>
      </div>
      
      {/* Grid skeleton */}
      <AGGridSkeleton rows={8} />
      
      {/* Pagination skeleton */}
      <div className="flex items-center justify-between">
        <Skeleton className="h-4 w-32" />
        <div className="flex space-x-2">
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-8" />
        </div>
      </div>
    </div>
  );
}

/**
 * Skeleton for insights grid with controls
 */
export function InsightsGridSkeleton() {
  return (
    <Card className="w-full">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div>
            <Skeleton className="h-6 w-48 mb-2" />
            <Skeleton className="h-4 w-64" />
          </div>
          <div className="flex space-x-2">
            <Skeleton className="h-10 w-32" />
            <Skeleton className="h-10 w-24" />
          </div>
        </div>
        
        {/* Quick filter skeleton */}
        <div className="flex items-center space-x-4 mt-4">
          <Skeleton className="h-10 w-64" />
          <Skeleton className="h-10 w-32" />
        </div>
      </CardHeader>
      
      <CardContent>
        <AGGridSkeleton rows={12} />
      </CardContent>
    </Card>
  );
}

/**
 * Skeleton for category analysis
 */
export function CategoryAnalysisSkeleton() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <Skeleton className="h-8 w-48 mb-2" />
          <Skeleton className="h-4 w-64" />
        </div>
        <Skeleton className="h-10 w-32" />
      </div>
      
      {/* Stats cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {Array.from({ length: 3 }).map((_, index) => (
          <Card key={index} className="p-4">
            <Skeleton className="h-4 w-24 mb-2" />
            <Skeleton className="h-8 w-16 mb-1" />
            <Skeleton className="h-3 w-20" />
          </Card>
        ))}
      </div>
      
      {/* Market leaders */}
      <Card className="p-6">
        <Skeleton className="h-6 w-32 mb-4" />
        <div className="space-y-3">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Skeleton className="h-8 w-8 rounded-full" />
                <div>
                  <Skeleton className="h-4 w-32 mb-1" />
                  <Skeleton className="h-3 w-24" />
                </div>
              </div>
              <Skeleton className="h-4 w-16" />
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
}

/**
 * Skeleton for loading overlay
 */
export function LoadingOverlaySkeleton() {
  return (
    <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="flex flex-col items-center space-y-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <Skeleton className="h-4 w-32" />
      </div>
    </div>
  );
}
