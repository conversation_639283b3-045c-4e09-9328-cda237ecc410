import { Card, CardContent, CardHeader } from '@kit/ui/card';
import { Skeleton } from '~/components/ui/skeleton';

/**
 * Skeleton for dashboard cards
 */
export function DashboardCardSkeleton() {
  return (
    <Card className="p-6 border border-gray-200">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <Skeleton className="h-4 w-32 mb-2" />
          <Skeleton className="h-8 w-24 mb-1" />
          <Skeleton className="h-3 w-28" />
        </div>
        <div className="rounded-full border border-gray-200 p-3">
          <Skeleton className="h-6 w-6 rounded-full" />
        </div>
      </div>
    </Card>
  );
}

/**
 * Skeleton for pricing cards
 */
export function PricingCardSkeleton() {
  return (
    <Card className="p-6 border border-gray-200">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <Skeleton className="h-4 w-36 mb-2" />
          <Skeleton className="h-8 w-32 mb-1" />
          <Skeleton className="h-3 w-24" />
        </div>
        <div className="rounded-full border border-gray-200 p-3">
          <Skeleton className="h-6 w-6 rounded-full" />
        </div>
      </div>
    </Card>
  );
}

/**
 * Skeleton for chart components
 */
export function ChartSkeleton({ height = 300 }: { height?: number }) {
  return (
    <Card className="p-6 border border-gray-200">
      <CardHeader className="pb-4">
        <Skeleton className="h-6 w-48 mb-2" />
        <Skeleton className="h-4 w-64" />
      </CardHeader>
      <CardContent>
        <Skeleton className={`w-full rounded-lg`} style={{ height: `${height}px` }} />
      </CardContent>
    </Card>
  );
}

/**
 * Skeleton for market share chart with legend
 */
export function MarketShareChartSkeleton() {
  return (
    <Card className="p-6 border border-gray-200 flex items-center justify-center w-full max-w-full">
      <div className="flex flex-row w-full items-center justify-center gap-4">
        {/* Pie Chart Skeleton */}
        <div className="h-[300px] w-[300px] flex items-center justify-center">
          <Skeleton className="h-[280px] w-[280px] rounded-full" />
        </div>
        {/* Legend Skeleton */}
        <div className="flex flex-col gap-2">
          {Array.from({ length: 5 }).map((_, idx) => (
            <div key={idx} className="flex items-center gap-2">
              <Skeleton className="h-3 w-3 rounded-full" />
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-3 w-8" />
            </div>
          ))}
        </div>
      </div>
    </Card>
  );
}

/**
 * Skeleton for detailed analysis cards
 */
export function DetailedAnalysisCardSkeleton() {
  return (
    <Card className="p-6 border border-gray-200">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <Skeleton className="h-4 w-40 mb-3" />
          <div className="space-y-2 max-h-64">
            {Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="flex justify-between items-center">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-4 w-20" />
              </div>
            ))}
          </div>
        </div>
        <div className="rounded-full border border-gray-200 p-3 ml-4">
          <Skeleton className="h-6 w-6 rounded-full" />
        </div>
      </div>
    </Card>
  );
}

/**
 * Complete dashboard skeleton
 */
export function DashboardSkeleton() {
  return (
    <div className="flex flex-col space-y-4 pb-16 duration-500 animate-in fade-in">
      {/* Main metrics cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <DashboardCardSkeleton />
        <DashboardCardSkeleton />
        <DashboardCardSkeleton />
        <DashboardCardSkeleton />
      </div>

      {/* Pricing intelligence cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
        <PricingCardSkeleton />
        <PricingCardSkeleton />
        <PricingCardSkeleton />
      </div>

      {/* Detailed analysis cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mt-6">
        <DetailedAnalysisCardSkeleton />
        <MarketShareChartSkeleton />
      </div>

      {/* Additional insights card */}
      <Card className="p-6 border border-gray-200 mt-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <Skeleton className="h-4 w-48 mb-3" />
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {Array.from({ length: 4 }).map((_, index) => (
                <div key={index}>
                  <Skeleton className="h-4 w-20 mb-1" />
                  <Skeleton className="h-5 w-16" />
                </div>
              ))}
            </div>
          </div>
          <div className="rounded-full border border-gray-200 p-3 ml-4">
            <Skeleton className="h-6 w-6 rounded-full" />
          </div>
        </div>
      </Card>
    </div>
  );
}
