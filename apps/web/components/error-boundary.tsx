'use client';

import React, { Component, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON>riangle, RefreshCw, Home } from 'lucide-react';
import { Button } from '@kit/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Alert, AlertDescription } from '@kit/ui/alert';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  showDetails?: boolean;
  title?: string;
  description?: string;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
}

export class DataErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Call the onError callback if provided
    this.props.onError?.(error, errorInfo);

    // Log to monitoring service (if available)
    if (typeof window !== 'undefined' && 'captureException' in window) {
      (window as any).captureException(error, {
        contexts: {
          react: {
            componentStack: errorInfo.componentStack,
          },
        },
      });
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  handleGoHome = () => {
    window.location.href = '/home';
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const { error, errorInfo } = this.state;
      const { showDetails = false, title, description } = this.props;

      return (
        <Card className="mx-auto max-w-2xl">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-xl font-semibold text-gray-900">
              {title || 'Something went wrong'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-center text-gray-600">
              {description || 'We encountered an unexpected error while loading this content.'}
            </p>

            {showDetails && error && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription className="font-mono text-sm">
                  <details>
                    <summary className="cursor-pointer font-sans font-medium">
                      Error Details
                    </summary>
                    <div className="mt-2 space-y-2">
                      <div>
                        <strong>Error:</strong> {error.message}
                      </div>
                      {error.stack && (
                        <div>
                          <strong>Stack:</strong>
                          <pre className="mt-1 whitespace-pre-wrap text-xs">
                            {error.stack}
                          </pre>
                        </div>
                      )}
                      {errorInfo?.componentStack && (
                        <div>
                          <strong>Component Stack:</strong>
                          <pre className="mt-1 whitespace-pre-wrap text-xs">
                            {errorInfo.componentStack}
                          </pre>
                        </div>
                      )}
                    </div>
                  </details>
                </AlertDescription>
              </Alert>
            )}

            <div className="flex justify-center space-x-3">
              <Button onClick={this.handleRetry} variant="default">
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again
              </Button>
              <Button onClick={this.handleGoHome} variant="outline">
                <Home className="mr-2 h-4 w-4" />
                Go Home
              </Button>
            </div>
          </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}

/**
 * Simple error boundary for inline components
 */
export function SimpleErrorBoundary({ 
  children, 
  fallback 
}: { 
  children: ReactNode; 
  fallback?: ReactNode;
}) {
  return (
    <DataErrorBoundary
      fallback={
        fallback || (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Failed to load this component. Please try refreshing the page.
            </AlertDescription>
          </Alert>
        )
      }
    >
      {children}
    </DataErrorBoundary>
  );
}

/**
 * Error boundary specifically for data fetching components
 */
export function DataFetchErrorBoundary({ children }: { children: ReactNode }) {
  return (
    <DataErrorBoundary
      title="Failed to Load Data"
      description="We couldn't load the requested data. This might be due to a network issue or server problem."
      showDetails={process.env.NODE_ENV === 'development'}
    >
      {children}
    </DataErrorBoundary>
  );
}

/**
 * Error boundary for chart/visualization components
 */
export function ChartErrorBoundary({ children }: { children: ReactNode }) {
  return (
    <DataErrorBoundary
      title="Chart Loading Error"
      description="We couldn't render this chart. The data might be malformed or there could be a rendering issue."
      fallback={
        <Card className="flex h-64 items-center justify-center">
          <CardContent className="text-center">
            <AlertTriangle className="mx-auto mb-2 h-8 w-8 text-yellow-500" />
            <p className="text-sm text-gray-600">Chart unavailable</p>
          </CardContent>
        </Card>
      }
    >
      {children}
    </DataErrorBoundary>
  );
}
