'use client';

import { useState } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { createQueryClient } from '~/lib/react-query/client';

export function ReactQueryProvider(props: React.PropsWithChildren) {
  const [queryClient] = useState(() => createQueryClient());

  return (
    <QueryClientProvider client={queryClient}>
      {props.children}
      {/* React Query DevTools can be added later when the package is installed */}
    </QueryClientProvider>
  );
}
