import { Heading } from '@kit/ui/heading';
import { Card, CardContent } from '@kit/ui/card';

export default function CompetitorActivity() {
  // Declare the required data in JSON format
  const activities = [
    {
      id: 1,
      appName: 'App One',
      appLogo: 'https://via.placeholder.com/40',
      description: 'New feature: Dark Mode',
      impact: 'Positive user response',
      timeAgo: '2 hours ago',
    },
    {
      id: 2,
      appName: 'App Two',
      appLogo: 'https://via.placeholder.com/40',
      description: 'Updated pricing structure',
      impact: 'Price increased by 10%',
      timeAgo: '5 hours ago',
    },
    {
      id: 3,
      appName: 'App Three',
      appLogo: 'https://via.placeholder.com/40',
      description: 'Significant rating change',
      impact: 'Rating dropped from 4.8 to 4.2',
      timeAgo: '1 day ago',
    },
    // You can add more activities to see the effect of the grid rearranging itself
    {
      id: 4,
      appName: 'App Four',
      appLogo: 'https://via.placeholder.com/40',
      description: 'Bug fixes and performance improvements',
      impact: 'Improved app stability',
      timeAgo: '2 days ago',
    },
  ];

  return (
    <div className="pb-10">
      {/* Page heading */}
      <Heading level={3} className="mb-6 text-2xl font-bold text-gray-800">
        Competitor Activity
      </Heading>

      {/* Card container */}
      <Card className="p-6 bg-gray-50 shadow-lg rounded-xl">
        <CardContent>
          {/* Grid container: max 3 items per row */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Dynamically generate cards */}
            {activities.map((activity) => (
              <div
                key={activity.id}
                className="p-4 bg-white rounded-lg shadow-md flex flex-col relative hover:shadow-lg transition-shadow duration-300"
              >
                <div className="flex items-center">
                  <img
                    src={activity.appLogo}
                    alt={`${activity.appName} Logo`}
                    className="w-12 h-12 rounded-full mr-3"
                  />
                  <div>
                    <h3 className="font-semibold text-gray-800">
                      {activity.appName}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {activity.description}
                    </p>
                  </div>
                </div>
                <p className="mt-3 text-sm text-blue-600 font-medium">
                  {activity.impact}
                </p>
                <p className="absolute top-2 right-4 text-xs text-gray-400">
                  {activity.timeAgo}
                </p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
