import React from 'react';
import { Di<PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from '@kit/ui/dialog';
import { Card, CardContent } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { XCircle } from 'lucide-react';
import { ALERT_TYPES } from '../constants';
import type { Alert } from '../types';

interface ManageAlertsDialogProps {
  open: boolean;
  onClose: () => void;
  alerts: Alert[];
  onDeleteAlert: (id: number) => void;
  onToggleStatus: (id: number) => void;
}

export const ManageAlertsDialog: React.FC<ManageAlertsDialogProps> = ({
  open,
  onClose,
  alerts,
  onDeleteAlert,
  onToggleStatus
}) => {
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] bg-white">
        <DialogHeader>
          <DialogTitle>Manage Alerts</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          {alerts.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No alerts configured yet
            </div>
          ) : (
            alerts.map((alert) => {
              const alertType = ALERT_TYPES.find(t => t.value === alert.alertType);
              return (
                <Card key={alert.id} className="bg-gray-50">
                  <CardContent className="flex justify-between items-center py-4">
                    <div className="flex items-center gap-2">
                      {alertType?.icon}
                      <span className="font-medium">
                        {alertType?.label}
                      </span>
                      <span className="text-gray-500">
                        ({alert.threshold}% - {alert.frequency})
                      </span>
                      <span className={`px-2 py-1 rounded text-xs ${
                        alert.status === 'active' 
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {alert.status.toUpperCase()}
                      </span>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="ghost"
                        onClick={() => onToggleStatus(alert.id)}
                        className={alert.status === 'active' ? 'text-yellow-500' : 'text-green-500'}
                      >
                        {alert.status === 'active' ? 'Pause' : 'Activate'}
                      </Button>
                      <Button
                        variant="ghost"
                        onClick={() => onDeleteAlert(alert.id)}
                        className="text-red-500 hover:text-red-600"
                      >
                        <XCircle className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};