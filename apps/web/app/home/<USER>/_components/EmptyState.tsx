import React from 'react';
import { Card, CardContent } from '@kit/ui/card';
import { But<PERSON> } from '@kit/ui/button';
import { Bell } from 'lucide-react';

interface EmptyStateProps {
  onCreateAlert: () => void;
}

export const EmptyState: React.FC<EmptyStateProps> = ({ onCreateAlert }) => {
  return (
    <Card className="bg-white">
      <CardContent className="flex flex-col items-center justify-center py-12">
        <Bell className="h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium mb-2">No Alerts Configured</h3>
        <p className="text-gray-500 mb-4">
          Create your first alert to start monitoring your metrics
        </p>
        <Button
          onClick={onCreateAlert}
          className="bg-blue-600 text-white hover:bg-blue-700"
        >
          Create First Alert
        </Button>
      </CardContent>
    </Card>
  );
};