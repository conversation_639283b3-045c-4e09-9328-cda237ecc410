import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@kit/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { Button } from '@kit/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { ALERT_TYPES, FREQUENCIES, SEVERITY_LEVELS } from '../constants';
import type { Alert as AlertType } from '../types';

interface AlertDialogFormProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (alert: Partial<AlertType>) => void;
}

export const AlertDialogForm: React.FC<AlertDialogFormProps> = ({ 
  open, 
  onClose, 
  onSubmit 
}) => {
  const [formData, setFormData] = useState<Partial<AlertType>>({
    alertType: '',
    threshold: 0,
    frequency: 'immediate',
    message: '',
    conditions: '',
    severity: 'medium'
  });

  const [error, setError] = useState<string>('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    
    if (!formData.alertType) {
      setError('Please select an alert type');
      return;
    }
    if (!formData.threshold || formData.threshold <= 0 || formData.threshold > 100) {
      setError('Threshold must be between 1 and 100');
      return;
    }

    onSubmit(formData);
    setFormData({
      alertType: '',
      threshold: 0,
      frequency: 'immediate',
      message: '',
      conditions: '',
      severity: 'medium'
    });
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] bg-white">
        <DialogHeader>
          <DialogTitle>Create New Alert</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          <div className="space-y-2">
            <Label>Alert Type</Label>
            <Select
              value={formData.alertType}
              onValueChange={(value) => setFormData({ ...formData, alertType: value })}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select alert type" />
              </SelectTrigger>
              <SelectContent>
                {ALERT_TYPES.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    <div className="flex items-center gap-2">
                      {type.icon}
                      <span>{type.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Severity</Label>
            <Select
              value={formData.severity}
              onValueChange={(value: AlertType['severity']) => 
                setFormData({ ...formData, severity: value })}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select severity" />
              </SelectTrigger>
              <SelectContent>
                {SEVERITY_LEVELS.map((level) => (
                  <SelectItem key={level.value} value={level.value}>
                    {level.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Threshold (%)</Label>
            <Input
              type="number"
              value={formData.threshold || ''}
              onChange={(e) => setFormData({ 
                ...formData, 
                threshold: parseInt(e.target.value) 
              })}
              min="1"
              max="100"
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <Label>Frequency</Label>
            <Select
              value={formData.frequency}
              onValueChange={(value) => setFormData({ ...formData, frequency: value })}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select frequency" />
              </SelectTrigger>
              <SelectContent>
                {FREQUENCIES.map((freq) => (
                  <SelectItem key={freq.value} value={freq.value}>
                    {freq.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Custom Message (Optional)</Label>
            <Input
              value={formData.message || ''}
              onChange={(e) => setFormData({ 
                ...formData, 
                message: e.target.value 
              })}
              className="w-full"
            />
          </div>

          <div className="flex justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-blue-600 text-white hover:bg-blue-700"
            >
              Create Alert
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
