import { PageHeader } from '@kit/ui/page';
import { Input } from '@kit/ui/input';
import { Search } from 'lucide-react';


import type { UserWorkspace } from '../_lib/server/load-user-workspace';

interface homepageheaderprops {
  workspace: UserWorkspace;
}

export function DashboardPageHeader(props: homepageheaderprops) {
  const { workspace } = props.workspace;
  return (
    <PageHeader title={`Hey, ${workspace.name}`} description={'Stay informed and ahead'}>
      <div className={'container mx-auto max-w-md mt-4'}>
        <div className={'relative'}>
          {/* Search Icon */}
          <Search
            className={'absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 '}
            size={20}
          />
          {/* Search Input */}
          <Input
            placeholder={'Search competitor'}
            className={
              'w-full rounded-lg border border-gray-300 px-10 py-3 text-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-300 shadow-md'
            }
          />
          
        </div>
      </div>
    </PageHeader>
  );
}
