import React from 'react';
import { Card, CardHeader, CardContent } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { XCircle, ArrowUp, ArrowDown } from 'lucide-react';
import { ALERT_TYPES } from '../constants';
import type { AlertNotification } from '../types';

interface NotificationCardProps {
  notification: AlertNotification;
  onDismiss: (id: number) => void;
  onStatusChange: (id: number, status: 'new' | 'read') => void;
}

export const NotificationCard: React.FC<NotificationCardProps> = ({ 
  notification,
  onDismiss,
  onStatusChange
}) => {
  const alertTypeInfo = ALERT_TYPES.find(type => type.value === notification.alertType);
  
  return (
    <Card className={`
      bg-white border transition-all duration-200
      ${notification.status === 'new' ? 'border-blue-200 shadow-md' : 'border-gray-100'}
    `}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="flex items-center gap-2">
          <div className={`text-sm font-medium flex items-center gap-2 ${alertTypeInfo?.color}`}>
            {alertTypeInfo?.icon}
            {alertTypeInfo?.label}
          </div>
          <span className={`text-sm px-2 py-1 rounded ${
            notification.severity === 'high' ? 'bg-red-100 text-red-800' :
            notification.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
            'bg-blue-100 text-blue-800'
          }`}>
            {notification.severity.toUpperCase()}
          </span>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-gray-500 text-xs">
            {new Date(notification.timestamp).toLocaleString()}
          </span>
          <button
            onClick={() => onDismiss(notification.id)}
            className="text-gray-400 hover:text-gray-600"
          >
            <XCircle className="h-4 w-4" />
          </button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <div className="text-sm text-gray-500">Current Value</div>
              <div className="font-medium text-lg flex items-center gap-1">
                {notification.value.toFixed(1)}%
                <span className={notification.metadata.trend === 'increasing' ? 'text-red-500' : 'text-green-500'}>
                  {notification.metadata.trend === 'increasing' ? (
                    <ArrowUp className="h-4 w-4" />
                  ) : (
                    <ArrowDown className="h-4 w-4" />
                  )}
                  {notification.metadata.changePercentage.toFixed(1)}%
                </span>
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-sm text-gray-500">Threshold</div>
              <div className="font-medium text-lg">
                {notification.threshold}%
              </div>
            </div>
          </div>
          
          <div className="text-sm text-gray-500 space-y-1">
            <div className="flex items-center gap-2">
              <span>Competitor:</span>
              <span className="font-medium">{notification.metadata.competitor}</span>
            </div>
            <div className="flex items-center gap-2">
              <span>Frequency:</span>
              <span className="font-medium">{notification.metadata.frequency}</span>
            </div>
            {notification.metadata.conditions && (
              <div className="mt-1 text-xs">
                {notification.metadata.conditions}
              </div>
            )}
          </div>
          
          <div className="flex justify-end">
            <Button
              variant="outline"
              onClick={() => onStatusChange(notification.id, notification.status === 'new' ? 'read' : 'new')}
              className="text-sm"
            >
              {notification.status === 'new' ? 'Mark as Read' : 'Mark as Unread'}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};