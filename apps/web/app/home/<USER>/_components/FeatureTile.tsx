"use client";
import React, { useState } from "react";
import { FiSettings } from "react-icons/fi";
import { FaFile } from "react-icons/fa";
import { TbReportAnalytics } from "react-icons/tb";
import { supabase } from "~/lib/supabase/client";
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
} from "@kit/ui/card";
import { Badge } from "@kit/ui/badge";

// Account Tile Component
function AccountTile() {
  return (
    <div className="flex flex-col space-y-4 p-6 bg-blue-100 shadow-lg rounded-lg hover:shadow-2xl transition-shadow duration-300">
      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-200 p-3">
        <FiSettings className="h-6 w-6 text-blue-700" />
      </div>
      <h3 className="text-lg font-bold text-gray-800">Account Setup</h3>
      <p className="text-gray-600 text-sm">Customize your settings with ease.</p>
      <button className="mt-auto px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-300">
        Learn More
      </button>
    </div>
  );
}

// Guide Tile Component
function GuideTile() {
  return (
    <div className="flex flex-col space-y-4 p-6 bg-green-100 shadow-lg rounded-lg hover:shadow-2xl transition-shadow duration-300">
      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-200 p-3">
        <FaFile className="h-6 w-6 text-green-700" />
      </div>
      <h3 className="text-lg font-bold text-gray-800">Guide & Tutorial</h3>
      <p className="text-gray-600 text-sm">Explore features with our tutorials.</p>
      <button className="mt-auto px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors duration-300">
        Learn More
      </button>
    </div>
  );
}

// Sample Report Tile Component
function SampleReportTile() {
  return (
    <div className="flex flex-col space-y-4 p-6 bg-yellow-100 shadow-lg rounded-lg hover:shadow-2xl transition-shadow duration-300">
      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-yellow-200 p-3">
        <TbReportAnalytics className="h-6 w-6 text-yellow-700" />
      </div>
      <h3 className="text-lg font-bold text-gray-800">Sample Reports</h3>
      <p className="text-gray-600 text-sm">View sample reports for insights.</p>
      <button className="mt-auto px-6 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors duration-300">
        Learn More
      </button>
    </div>
  );
}

// Feature Card Component
type BadgeVariant = "default" | "secondary" | "destructive" | "outline";

interface BadgeProps {
  text: string;
  variant: BadgeVariant;
}

interface FeatureCardProps {
  title: string;
  description: string;
  number: string;
  badge?: BadgeProps;
  buttons?: { text: string; className: string; onClick?: () => void }[];
}

const FeatureCard: React.FC<FeatureCardProps> = ({
  title,
  description,
  number,
  badge,
  buttons,
}) => (
  <Card className="group relative border border-gray-200 shadow-lg rounded-lg hover:shadow-2xl transition-shadow duration-300">
    <CardHeader className="p-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center justify-center h-10 w-10 rounded-full bg-primary-100 text-primary-600 text-xl font-bold">
          {number}
        </div>
        {badge?.text && badge.variant && (
          <Badge variant={badge.variant} className="text-xs px-2 py-1">
            {badge.text}
          </Badge>
        )}
      </div>
      <CardTitle className="mt-3 text-xl font-semibold">{title}</CardTitle>
    </CardHeader>
    <CardContent className="px-6 pb-6">
      <CardDescription className="text-gray-600 text-sm">
        {description}
      </CardDescription>
      <div className="mt-6 flex space-x-4">
        {buttons?.map((button, index) => (
          <button
            key={index}
            className={`px-4 py-2 rounded-md transition-colors duration-300 ${button.className}`}
            onClick={button.onClick}
          >
            {button.text}
          </button>
        ))}
      </div>
    </CardContent>
  </Card>
);

// BlockWithTiles Component
const BlockWithTiles: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [formData, setFormData] = useState({ id: "", name: "", url: "" });
  const [message, setMessage] = useState("");

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Map the formData field to match the table schema
    const { id, name, url } = formData;
    const competitorData = { id, name, url }; // changed from 'description' to 'url'

    console.log("Form Data before submitting:", competitorData);

    const { error } = await supabase.from("competitor").insert([competitorData]);

    if (error) {
      console.error("Error inserting competitor:", error.message);
      setMessage("Failed to add competitor: " + error.message);
    } else {
      setFormData({ id: "", name: "", url: "" });
      setIsModalOpen(false); // Close the modal after success
    }
  };

  const features: FeatureCardProps[] = [
    {
      title: "Add your first competitor",
      description: "Start by adding your main competitors from the main app.",
      number: "1",
      buttons: [
        {
          text: "Add Competitor",
          className: "bg-black text-white hover:bg-gray-800",
          onClick: () => setIsModalOpen(true),
        },
      ],
    },
    {
      title: "Set up alert preference",
      description: "Configure alert preferences to stay updated.",
      number: "2",
      buttons: [
        { text: "Configure Alert", className: "bg-primary text-white hover:bg-primary/80" },
      ],
    },
    {
      title: "Customize your dashboard",
      description: "Tailor the dashboard to your needs.",
      number: "3",
      buttons: [
        { text: "Customize View", className: "bg-primary text-white hover:bg-primary/80" },
      ],
    },
  ];

  return (
    <div>
      <section className="bg-background py-20">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-semibold text-center mb-8">
            Quick Setup Guide
          </h2>
          <div className="space-y-8">
            {features.map((feature, index) => (
              <FeatureCard key={index} {...feature} />
            ))}
          </div>
        </div>
      </section>

      {/* Modal for Adding Competitor */}
      {isModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white p-6 rounded-lg shadow-lg w-96">
            <h3 className="text-xl font-semibold mb-4">Add Competitor</h3>
            <form onSubmit={handleSubmit}>
              <div className="mb-4">
                <label className="block text-sm font-medium mb-2"> App ID:</label>
                <input
                  type="text"
                  name="id"
                  value={formData.id}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border rounded"
                  required
                />
              </div>
              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">App Name:</label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border rounded"
                  required
                />
              </div>
              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">App URL:</label>
                <textarea
                  name="url"
                  value={formData.url}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border rounded"
                  required
                ></textarea>
              </div>
              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={() => setIsModalOpen(false)}
                  className="px-4 py-2 bg-gray-500 text-white rounded-md"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Add
                </button>
              </div>
            </form>
            {message && (
              <p className="mt-4 text-center text-sm text-gray-600">{message}</p>
            )}
          </div>
        </div>
      )}

      {/* Feature Tiles Section */}
      <div className="w-full space-y-8 p-6">
        <div className="w-full grid gap-6 md:grid-cols-1 lg:grid-cols-3">
          <AccountTile />
          <GuideTile />
          <SampleReportTile />
        </div>
      </div>
    </div>
  );
};

export default BlockWithTiles;
