import { useState, useEffect, useCallback } from 'react';
import { createClient } from '@supabase/supabase-js';
import type { Alert, AlertNotification } from '../types';
import type { Database } from '../database.types';

interface UseSupabaseAlertsReturn {
  notifications: AlertNotification[];
  dismissNotification: (id: number) => void;
  updateNotificationStatus: (id: number, status: 'new' | 'read') => void;
  isLoading: boolean;
  error: Error | null;
}

export const useSupabaseAlerts = (
  alerts: Alert[],
  pollingInterval = 5000
): UseSupabaseAlertsReturn => {
  const [notifications, setNotifications] = useState<AlertNotification[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const SUPABASE_URL = 'https://ebhpbujfjbrbccusjaau.supabase.co';
  const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImViaHBidWpmamJyYmNjdXNqYWF1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg3NTA5MjAsImV4cCI6MjA1NDMyNjkyMH0.40BBo6Y5bfaf-fnl4n-hyFXVYYQIcYquhjyjrJ4ukTo';

  // ✅ Removed authentication configuration
  const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_ANON_KEY);

  // Fetch notifications from Supabase
  const fetchNotifications = useCallback(async () => {
    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from('alert_notifications')
        .select('*')
        .order('timestamp', { ascending: false })
        .limit(50);

      if (error) throw error;

      if (data) {
        const formattedNotifications: AlertNotification[] = data.map(notification => ({
          id: notification.id,
          alertId: notification.alert_id,
          timestamp: new Date(notification.timestamp),
          message: notification.message,
          alertType: notification.alert_type,
          value: notification.value,
          threshold: notification.threshold,
          severity: notification.severity as 'low' | 'medium' | 'high',
          status: notification.status as 'new' | 'read',
          metadata: {
            metric: notification.metric || '',
            frequency: notification.frequency,
            conditions: notification.conditions,
            trend: notification.trend as 'increasing' | 'decreasing',
            changePercentage: notification.change_percentage,
            competitor: notification.competitor
          }
        }));
        setNotifications(formattedNotifications);
      }
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch notifications'));
    } finally {
      setIsLoading(false);
    }
  }, [supabase]);

  // Set up real-time subscription
  useEffect(() => {
    const channel = supabase
      .channel('alert_notifications')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'alert_notifications'
        },
        () => {
          fetchNotifications();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [supabase, fetchNotifications]);

  // Poll for new notifications
  useEffect(() => {
    fetchNotifications();
    const intervalId = setInterval(fetchNotifications, pollingInterval);
    
    return () => clearInterval(intervalId);
  }, [fetchNotifications, pollingInterval]);

  const dismissNotification = useCallback(async (id: number) => {
    try {
      const { error } = await supabase
        .from('alert_notifications')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setNotifications(prev => prev.filter(n => n.id !== id));
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to dismiss notification'));
    }
  }, [supabase]);

  const updateNotificationStatus = useCallback(async (id: number, status: 'new' | 'read') => {
    try {
      const { error } = await supabase
        .from('alert_notifications')
        .update({ status })
        .eq('id', id);

      if (error) throw error;

      setNotifications(prev =>
        prev.map(notification =>
          notification.id === id ? { ...notification, status } : notification
        )
      );
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to update notification status'));
    }
  }, [supabase]);

  return {
    notifications,
    dismissNotification,
    updateNotificationStatus,
    isLoading,
    error
  };
};
