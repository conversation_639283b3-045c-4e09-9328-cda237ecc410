'use client';

import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { Button } from '@kit/ui/button';
import { Card, CardContent } from '@kit/ui/card';
import { Bell, Settings2 } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { NotificationCard } from '../_components/NotificationCard';
import { AlertDialogForm } from '../_components/AlertDialogForm';
import { ManageAlertsDialog } from '../_components/ManageAlertsDialog';
import { SearchBar } from '../_components/SearchBar';
import { EmptyState } from '../_components/EmptyState';
import { useSupabaseAlerts } from '../hooks/useSupabaseAlerts';
import type { Alert as AlertType, AlertNotification } from '../types';
import { createClient } from '@supabase/supabase-js';
import type { Database } from '../database.types';

const SUPABASE_URL = 'https://ebhpbujfjbrbccusjaau.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImViaHBidWpmamJyYmNjdXNqYWF1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg3NTA5MjAsImV4cCI6MjA1NDMyNjkyMH0.40BBo6Y5bfaf-fnl4n-hyFXVYYQIcYquhjyjrJ4ukTo';
const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_ANON_KEY);

export default function AlertsPageClient() {
  const [alerts, setAlerts] = useState<AlertType[]>([]);
  const [createAlertOpen, setCreateAlertOpen] = useState(false);
  const [manageAlertsOpen, setManageAlertsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isFetching, setIsFetching] = useState(true);
  
  const { 
    notifications, 
    dismissNotification, 
    updateNotificationStatus,
    isLoading,
    error 
  } = useSupabaseAlerts(alerts);

  const fetchAlerts = useCallback(async () => {
    setIsFetching(true);
    const { data, error } = await supabase
      .from('alerts')
      .select('*')
      .order('created_at', { ascending: false });

    if (!error && data) {
      setAlerts(data.map(alert => ({
        id: alert.id,
        alertType: alert.alert_type,
        threshold: alert.threshold,
        frequency: alert.frequency,
        message: alert.message || undefined,
        metric: alert.metric || undefined,
        conditions: alert.conditions || undefined,
        createdAt: new Date(alert.created_at),
        status: alert.status as 'active' | 'paused',
        severity: alert.severity as 'low' | 'medium' | 'high'
      })));
    }
    setIsFetching(false);
  }, []);

  useEffect(() => {
    fetchAlerts();
  }, [fetchAlerts]);

  const filteredNotifications = useMemo(() => {
    return notifications.filter(notification => 
      searchTerm === '' ||
      notification.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
      notification.metadata?.competitor?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [notifications, searchTerm]);

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header Section */}
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">Alert Monitoring</h1>
          <div className="flex gap-4">
            <Button
              onClick={() => setCreateAlertOpen(true)}
              className="bg-blue-600 text-white hover:bg-blue-700"
            >
              <Bell className="h-4 w-4 mr-2" />
              Create Alert
            </Button>
            <Button
              variant="outline"
              onClick={() => setManageAlertsOpen(true)}
            >
              <Settings2 className="h-4 w-4 mr-2" />
              Manage Alerts
            </Button>
          </div>
        </div>

        <SearchBar searchTerm={searchTerm} onSearchChange={setSearchTerm} />

        {error && (
          <Alert variant="destructive">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error.message}</AlertDescription>
          </Alert>
        )}

        {isFetching ? (
          <Card className="bg-white">
            <CardContent className="flex justify-center py-8">
              <p className="text-gray-500 animate-pulse">Loading alerts...</p>
            </CardContent>
          </Card>
        ) : alerts.length === 0 ? (
          <EmptyState onCreateAlert={() => setCreateAlertOpen(true)} />
        ) : filteredNotifications.length === 0 ? (
          <Card className="bg-white">
            <CardContent className="flex justify-center py-8">
              <p className="text-gray-500">No matching alerts found</p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {filteredNotifications.map(notification => (
              <NotificationCard
                key={notification.id}
                notification={notification}
                onDismiss={dismissNotification}
                onStatusChange={updateNotificationStatus}
              />
            ))}
          </div>
        )}

        <AlertDialogForm
          open={createAlertOpen}
          onClose={() => setCreateAlertOpen(false)}
          onSubmit={(alertData) => {
            console.log('New alert data:', alertData);
            fetchAlerts(); // Refresh alerts after creation
          }}
        />


      </div>
    </div>
  );
} 