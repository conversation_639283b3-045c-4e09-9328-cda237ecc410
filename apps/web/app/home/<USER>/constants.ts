import React from 'react';
import { 
  TrendingDown,
  MessageSquare,
  PlayCircle,
  Users
} from 'lucide-react';

interface AlertType {
  value: string;
  label: string;
  icon: React.ReactNode;
  description: string;
  color: string;
  severityLevels: {
    high: number;
    medium: number;
    low: number;
  };
}

export const ALERT_TYPES: AlertType[] = [
  {
    value: 'rating-drop',
    label: 'Rating Drop',
    icon: React.createElement(TrendingDown, { className: "h-4 w-4" }),
    description: 'Alerts when ratings drop below threshold',
    color: 'text-rose-500',
    severityLevels: {
      high: 20,
      medium: 10,
      low: 5
    }
  },
  {
    value: 'negative-reviews',
    label: 'Negative Reviews',
    icon: React.createElement(MessageSquare, { className: "h-4 w-4" }),
    description: 'Monitors increase in negative reviews',
    color: 'text-orange-500',
    severityLevels: {
      high: 30,
      medium: 20,
      low: 10
    }
  },
  {
    value: 'response-time',
    label: 'Response Time',
    icon: React.createElement(PlayCircle, { className: "h-4 w-4" }),
    description: 'Tracks customer response times',
    color: 'text-blue-500',
    severityLevels: {
      high: 48,
      medium: 24,
      low: 12
    }
  },
  {
    value: 'sentiment-drop',
    label: 'Sentiment Drop',
    icon: React.createElement(Users, { className: "h-4 w-4" }),
    description: 'Monitors customer sentiment changes',
    color: 'text-purple-500',
    severityLevels: {
      high: 25,
      medium: 15,
      low: 5
    }
  }
];

export const FREQUENCIES = [
  { value: 'realtime', label: 'Real-time' },
  { value: 'immediate', label: 'Immediate' },
  { value: 'hourly', label: 'Hourly' },
  { value: 'daily', label: 'Daily' },
  { value: 'weekly', label: 'Weekly' }
] as const;

export const SEVERITY_LEVELS = [
  { value: 'high', label: 'High Priority' },
  { value: 'medium', label: 'Medium Priority' },
  { value: 'low', label: 'Low Priority' }
] as const;