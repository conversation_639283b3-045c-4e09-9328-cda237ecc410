import { PageBody } from '@kit/ui/page';
import CategoryPageClient from './CategoryPageClient';

export default function CategoryPage() {
  // Data fetching would ideally happen here in a real Server Component,
  // and then passed as props to CategoryPageClient.
  // For now, CategoryPageClient still handles its own fetching.

  return (
    <PageBody>
      {/* Add Category Dialog component here if it's a Server Component */}
      {/* Otherwise, it should be inside CategoryPageClient or another Client Component */}
      <CategoryPageClient />
    </PageBody>
  );
} 