import React, { useEffect, useState } from 'react';
// import { Progress } from '@kit/ui/shadcn/progress'; // Remove import
import { TrendingUp, TrendingDown } from 'lucide-react'; // Import icons

interface CategoryMarketShareData {
  name: string;
  leader: string;
  marketShare: string; // e.g., "35%"
  leaderClassification: string;
  change: {
    value: string; // e.g., "+8%", "-2%"
    isPositive: boolean;
  };
  progress: number;
}

interface MarketShareByCategoryProps {
  marketShareData: CategoryMarketShareData[];
  loading: boolean;
  error: string | null;
  onCategoryClick: (categoryName: string) => void;
}

export function MarketShareByCategory({ marketShareData, loading, error, onCategoryClick }: MarketShareByCategoryProps) {
  if (loading) {
    return <div className="p-4 text-center">Loading market share data...</div>;
  }

  if (error) {
    return <div className="p-4 text-red-500 text-center">Error: {error}</div>;
  }

  if (!marketShareData || marketShareData.length === 0) {
    return <div className="p-4 text-center">No market share data available for tracked competitors in categories.</div>;
  }

  const parsePercentage = (percentageString: string): number => {
    const numberMatch = percentageString.match(/\d+(\.\d+)?/);
    return numberMatch ? parseFloat(numberMatch[0]) : 0;
  };

  return (
    <div className="space-y-4">
      <h3 className="text-xl font-bold">Market Share by Category</h3>
      <p className="text-sm text-gray-500">Leading apps and their market dominance</p>
      <div className="grid grid-cols-1 gap-4">
        {marketShareData.map((categoryData) => {
          const marketShareValue = parsePercentage(categoryData.marketShare);
          const changeValue = categoryData.change?.value;
          const isChangePositive = categoryData.change?.isPositive;

          return (
            <div 
              key={categoryData.name} 
              className="p-4 border rounded-lg shadow-sm cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700"
              onClick={() => onCategoryClick(categoryData.name)}
            >
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h4 className="font-semibold text-base">{categoryData.name}</h4>
                  <p className="text-sm text-gray-600">Leader: {categoryData.leader}</p>
                </div>
                <div className="flex flex-col items-end">
                  <div className="font-semibold text-base">{categoryData.marketShare}</div>
                  {changeValue && (
                    <div className={`text-xs flex items-center ${isChangePositive ? 'text-green-500' : 'text-red-500'}`}>
                      {isChangePositive ? <TrendingUp className="w-3 h-3 mr-0.5" /> : <TrendingDown className="w-3 h-3 mr-0.5" />}
                      {changeValue}
                    </div>
                  )}
                </div>
              </div>
              <progress value={marketShareValue} max="100" className="w-full h-2 rounded-full overflow-hidden bg-gray-200 accent-blue-600"></progress>
            </div>
          );
        })}
      </div>
    </div>
  );
} 