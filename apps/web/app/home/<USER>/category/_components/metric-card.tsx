'use client';

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@kit/ui/card';
import { TrendingDown, TrendingUp } from 'lucide-react';

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: {
    value: string | number;
    isPositive: boolean;
  };
  icon?: React.ReactNode;
}

export function MetricCard({ title, value, change, icon }: MetricCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">
          {title}
        </CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {change && (
          <p className={`text-xs ${change.isPositive ? 'text-green-500' : 'text-red-500'}`}>
            {change.isPositive ? <TrendingUp className="inline-block w-3 h-3 mr-0.5" /> : <TrendingDown className="inline-block w-3 h-3 mr-0.5" />}
            {change.value}
          </p>
        )}
      </CardContent>
    </Card>
  );
} 