'use client';

import React, { useEffect, useState } from 'react';
import { Card } from '@kit/ui/card';
import { App } from '../../../../types/data';
import { CategoryAnalysisSkeleton } from '~/components/skeletons/grid-skeleton';

interface MarketLeader {
  app_id: number;
  app_name: string;
  marketShare: number;
  rating: number;
  review_count: number;
  categories: string[];
  qualityScore: number;
  leadershipScore: number;
}

interface MarketLeadersProps {
  categoryFeatureHeading?: string; // Make it optional for initial load, but required for pop-up
}

export function MarketLeaders({ categoryFeatureHeading }: MarketLeadersProps) {
  const [marketLeaders, setMarketLeaders] = useState<MarketLeader[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchMarketLeaders() {
      try {
        setLoading(true);
        // Adjust the fetch URL to include the category heading if provided
        const url = categoryFeatureHeading ? `/api/market-leaders?category=${encodeURIComponent(categoryFeatureHeading)}` : '/api/market-leaders';
        const response = await fetch(url);
        
        if (!response.ok) {
          throw new Error('Failed to fetch market leaders');
        }
        
        const data = await response.json();
        setMarketLeaders(data);
      } catch (err: any) {
        // Error handling - error state will be displayed to user
        setError(err.message || 'Failed to fetch market leaders');
      } finally {
        setLoading(false);
      }
    }

    fetchMarketLeaders();
  }, [categoryFeatureHeading]);

  if (loading) {
    return <CategoryAnalysisSkeleton />;
  }

  if (error) {
    return <Card className="p-4 text-red-500">Error: {error}</Card>;
  }

  if (!marketLeaders || marketLeaders.length === 0) {
    return <Card className="p-4">No market leaders found.</Card>;
  }

  return (
    <Card className="p-4">
      <div className="space-y-4">
        {marketLeaders.map((leader) => (
          <div key={leader.app_id} className="border rounded-lg p-4">
            <div className="flex justify-between items-start mb-2">
              <div>
                <h4 className="font-semibold">{leader.app_name}</h4>
                <p className="text-sm text-gray-600">
                  {leader.categories.join(', ')}
                </p>
              </div>
              <div className="text-right">
                <div className="font-semibold">{leader.marketShare.toFixed(1)}%</div>
                <div className="text-sm text-gray-600">
                  {leader.review_count.toLocaleString()} reviews
                </div>
                <div className="text-sm text-gray-600">
                  Rating: {leader.rating.toFixed(1)}
                </div>
              </div>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full"
                style={{ width: `${leader.marketShare}%` }}
              />
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
} 