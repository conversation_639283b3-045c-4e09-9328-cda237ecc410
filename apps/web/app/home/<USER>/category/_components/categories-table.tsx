'use client';

import React, { useEffect, useState } from 'react';
import { Card } from '@kit/ui/card';
import { TrendingUp, TrendingDown } from 'lucide-react'; // Assuming these icons are needed for change indication

// Define the structure for the market share data per category (should match API response)
interface CategoryMarketShareData {
  name: string; // The name of the category (e.g., "Reviews", "Email Marketing")
  leader: string; // The leading competitor in this category among the tracked ones (e.g., "Judge.me", "Klaviyo")
  marketShare: string; // The market share percentage (e.g., "35%", "42%")
  change: {
    value: string; // The change in market share (e.g., "+8%", "-2%")
    isPositive: boolean; // A boolean indicating if the change is positive or negative
  };
  progress: number; // Keeping this from the API structure, though not used in this table
}

export function CategoriesTable() {
  const [categoriesData, setCategoriesData] = useState<CategoryMarketShareData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchCategoryMarketShare() {
      try {
        setLoading(true);
        // Fetch data from the categories API endpoint
        const res = await fetch('/api/categories');
        if (!res.ok) throw new Error('Failed to fetch category market share data');
        const data: CategoryMarketShareData[] = await res.json();
        setCategoriesData(data);
      } catch (err: any) {
        setError(err.message || 'Unknown error');
      } finally {
        setLoading(false);
      }
    }
    fetchCategoryMarketShare();
  }, []);

  if (loading) {
    return <Card className="p-4 mt-8">Loading category market share...</Card>;
  }
  if (error) {
    return <Card className="p-4 mt-8 text-red-500">Error: {error}</Card>;
  }
  if (!categoriesData || categoriesData.length === 0) {
    return <Card className="p-4 mt-8">No category market share data found.</Card>;
  }

  return (
    <Card className="p-4 mt-8">
      <div className="font-bold text-lg mb-4">Market Share by Category</div>
      <div className="overflow-x-auto">
        <table className="min-w-full text-sm border-separate border-spacing-y-2">
          <thead>
            <tr className="text-gray-500 text-xs">
              <th className="text-left px-4 py-2">Category Name</th>
              <th className="text-left px-4 py-2">Leader</th>
              <th className="text-left px-4 py-2">Market Share</th>
              <th className="text-center px-4 py-2">Change</th>
              {/* Add Actions column if needed later */}
              {/* <th className="text-center px-4 py-2">Actions</th> */}
            </tr>
          </thead>
          <tbody>
            {categoriesData.map((category) => (
              <tr key={category.name} className="bg-white rounded-lg shadow-sm">
                <td className="px-4 py-2 font-medium">
                  {category.name}
                </td>
                <td className="px-4 py-2">
                  {category.leader}
                </td>
                <td className="px-4 py-2">
                  {category.marketShare}
                </td>
                <td className="px-4 py-2 text-center">
                   <span className={`inline-flex items-center gap-1 text-xs ${category.change.isPositive ? 'text-green-500' : 'text-red-500'}`}>
                     {category.change.isPositive ? <TrendingUp className="w-3 h-3" /> : <TrendingDown className="w-3 h-3" />}
                     {category.change.value}
                   </span>
                </td>
                 {/* Add Action button if needed later */}
                 {/* <td className="px-4 py-2 text-center">
                   <Eye className="w-5 h-5 text-blue-500 cursor-pointer hover:opacity-80" />
                 </td> */}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </Card>
  );
} 