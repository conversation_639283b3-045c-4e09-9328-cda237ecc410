'use client';

import React, { useEffect, useState } from 'react';
import { PageBody } from '@kit/ui/page';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { TrendingDown, TrendingUp, Mail } from 'lucide-react';
import { Button } from '@kit/ui/button';
// Import the new table component
import { MarketShareByCategory } from './_components/market-share-by-category'; // Import the MarketShareByCategory component
import { MarketLeaders } from './_components/market-leaders';

// Define MetricCard component directly (or import if resolved)
interface MetricCardProps {
  title: string;
  value: string | number;
  change?: {
    value: string | number;
    isPositive: boolean;
  };
  icon?: React.ReactNode;
}

function MetricCard({ title, value, change, icon }: MetricCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">
          {title}
        </CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {change && (
          <p className={`text-xs ${change.isPositive ? 'text-green-500' : 'text-red-500'}`}>
            {change.isPositive ? <TrendingUp className="inline-block w-3 h-3 mr-0.5" /> : <TrendingDown className="inline-block w-3 h-3 mr-0.5" />}
            {change.value}
          </p>
        )}
      </CardContent>
    </Card>
  );
}

// Define a basic type for competitor data needed for metrics
interface CompetitorMetricData {
  id?: string;
  app_id?: string | number;
  categories?: string[];
}

// Define the structure for the market share data per category here as it's used in this file now
interface CategoryMarketShareData {
  name: string; // The name of the category (e.g., "Reviews", "Email Marketing")
  leader: string; // The leading competitor in this category among the tracked ones (e.g., "Judge.me", "Klaviyo")
  marketShare: string; // The market share percentage (e.g., "35%", "42%")
  leaderClassification: string; // Add field for leader classification
  change: { // Illustrative change data as actual growth calculation isn't implemented
    value: string; 
    isPositive: boolean;
  };
  progress: number;
  averageGrowth: string; // Add field for average category growth to match API response
}

export default function CategoryPageClient() {
  const [competitors, setCompetitors] = useState<CompetitorMetricData[]>([]);
  const [loadingMetrics, setLoadingMetrics] = useState(true);
  const [errorMetrics, setErrorMetrics] = useState<string | null>(null);
  const [isSendingEmail, setIsSendingEmail] = useState(false);

  // State for market share data
  const [marketShareData, setMarketShareData] = useState<CategoryMarketShareData[]>([]);
  const [loadingMarketShare, setLoadingMarketShare] = useState(true);
  const [errorMarketShare, setErrorMarketShare] = useState<string | null>(null);

  // State for Market Leaders Pop-up
  const [showMarketLeadersPopup, setShowMarketLeadersPopup] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const handleCategoryClick = (categoryName: string) => {
    setSelectedCategory(categoryName);
    setShowMarketLeadersPopup(true);
  };

  const handleCloseMarketLeadersPopup = () => {
    setShowMarketLeadersPopup(false);
    setSelectedCategory(null);
  };

  useEffect(() => {
    async function fetchCompetitorsForMetrics() {
      try {
        setLoadingMetrics(true);
        // Fetch data from the competitors API endpoint
        const res = await fetch('/api/competitors');
        if (!res.ok) throw new Error('Failed to fetch competitors data for metrics');
        const data: CompetitorMetricData[] = await res.json();
        setCompetitors(data);
      } catch (err: any) {
        setErrorMetrics(err.message || 'Unknown error fetching competitor metrics');
      } finally {
        setLoadingMetrics(false);
      }
    }
    fetchCompetitorsForMetrics();
  }, []);

  // Effect to fetch market share data
  useEffect(() => {
    async function fetchMarketShareData() {
      try {
        setLoadingMarketShare(true);
        const res = await fetch('/api/categories');
        if (!res.ok) {
          throw new Error(`Error fetching market share data: ${res.statusText}`);
        }
        const data: CategoryMarketShareData[] = await res.json();
        console.log('API response data:', data); // Log the data
        setMarketShareData(data);
      } catch (err: any) {
        console.error('Error fetching market share data:', err);
        setErrorMarketShare(err.message || 'Unknown error fetching market share data');
      } finally {
        setLoadingMarketShare(false);
      }
    }
    fetchMarketShareData();
  }, []);

  // Calculate metrics based on fetched competitors data
  const totalAppsAnalyzed = competitors?.length || 0;
  const uniqueCategories = Array.from(new Set((competitors || []).flatMap(comp => comp.categories || []).filter(cat => cat !== '')));
  const activeCategories = uniqueCategories.length;

  // Calculate market leaders count from marketShareData
  const marketLeadersCount = marketShareData.filter(category =>
    category.leaderClassification === "Dominant Leader" || category.leaderClassification === "Strong Leader"
  ).length;

  // Calculate average category growth from marketShareData
  let totalGrowthSum = 0;
  let categoryCountWithGrowth = 0;

  marketShareData.forEach(category => {
    // Attempt to parse the numeric value from the averageGrowth string (e.g., "10.5%")
    const growthValue = parseFloat(category.averageGrowth);

    // Check if the parsed value is a valid finite number
    if (!isNaN(growthValue) && isFinite(growthValue)) {
      totalGrowthSum += growthValue;
      categoryCountWithGrowth++;
    } else if (category.averageGrowth === '+Infinity%') {
         totalGrowthSum += 1000; // Assign a large value for averaging to represent significant growth
         categoryCountWithGrowth++;
    } else if (category.averageGrowth === '-Infinity%') {
         totalGrowthSum -= 1000; // Assign a large negative value for significant decline
         categoryCountWithGrowth++;
    }
     // 'N/A' values are ignored in the average calculation
  });

  const overallAverageGrowth = categoryCountWithGrowth > 0 ? (totalGrowthSum / categoryCountWithGrowth) : NaN; // Calculate average, use NaN if no valid data

  // Format the average growth for display
  const calculatedAvgCategoryGrowth = !isNaN(overallAverageGrowth) && isFinite(overallAverageGrowth) ?
                              overallAverageGrowth.toFixed(1) + '%' :'N/A';
  // Determine overall loading and error state
  const overallLoading = loadingMetrics || loadingMarketShare;
  const overallError = errorMetrics || errorMarketShare;

  const handleSendEmail = async () => {
    try {
      setIsSendingEmail(true);
      const response = await fetch('/api/send-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          email: '<EMAIL>',
          name: 'Harinidevi' 
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send email');
      }

      // Show success message or handle response
      alert('Email sent successfully!');
    } catch (error) {
      console.error('Error sending email:', error);
      alert('Failed to send email. Please try again.');
    } finally {
      setIsSendingEmail(false);
    }
  };

  if (overallLoading) {
      return <Card className="p-4">Loading data...</Card>;
  }

  if (overallError) {
      return <Card className="p-4 text-red-500">Error loading data: {overallError}</Card>;
  }

  return (
    <>
      <div className="flex justify-end mb-6">
        <Button
          onClick={handleSendEmail}
          disabled={isSendingEmail}
          className="border border-black bg-white text-black hover:bg-gray-100"
        >
          <Mail className="h-4 w-4 mr-2" />
          {isSendingEmail ? 'Sending...' : 'Send Email'}
        </Button>
      </div>
      <div className="space-y-6">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          <MetricCard title="Total Apps Analyzed" value={totalAppsAnalyzed} />
          <MetricCard title="Active Categories" value={activeCategories} icon={<span className="text-blue-500 text-sm font-semibold"></span>} />
          <MetricCard title="Market Leaders" value={marketLeadersCount} icon={<span className="text-blue-500 text-sm font-semibold"></span>} />
          <MetricCard title="Avg Category Growth" value={calculatedAvgCategoryGrowth} icon={<span className="text-blue-500 text-sm font-semibold"></span>} />
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-1 gap-6">
          <MarketShareByCategory 
            marketShareData={marketShareData} 
            loading={loadingMarketShare} 
            error={errorMarketShare}
            onCategoryClick={handleCategoryClick}
          />
        </div>
      </div>

      {/* Market Leaders Pop-up */}
      {showMarketLeadersPopup && selectedCategory && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
          <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <button
              onClick={handleCloseMarketLeadersPopup}
              className="absolute top-3 right-3 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 text-lg font-semibold"
            >
              &times;
            </button>
            <div className="p-6">
              <h3 className="text-2xl font-bold mb-4">Market Leaders for {selectedCategory}</h3>
              <MarketLeaders categoryFeatureHeading={selectedCategory} />
            </div>
          </div>
        </div>
      )}
    </>
  );
} 