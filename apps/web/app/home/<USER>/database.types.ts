// lib/database.types.ts

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      alerts: {
        Row: {
          id: number
          alert_type: string
          threshold: number
          frequency: string
          message: string | null
          metric: string | null
          conditions: string | null
          created_at: string
          status: 'active' | 'paused'
          severity: 'low' | 'medium' | 'high'
          user_id: string
        }
        Insert: {
          id?: number
          alert_type: string
          threshold: number
          frequency: string
          message?: string | null
          metric?: string | null
          conditions?: string | null
          created_at?: string
          status: 'active' | 'paused'
          severity: 'low' | 'medium' | 'high'
          user_id: string
        }
        Update: {
          id?: number
          alert_type?: string
          threshold?: number
          frequency?: string
          message?: string | null
          metric?: string | null
          conditions?: string | null
          created_at?: string
          status?: 'active' | 'paused'
          severity?: 'low' | 'medium' | 'high'
          user_id?: string
        }
      }
      alert_notifications: {
        Row: {
          id: number
          alert_id: number
          timestamp: string
          message: string
          alert_type: string
          value: number
          threshold: number
          severity: 'low' | 'medium' | 'high'
          status: 'new' | 'read'
          metric: string | null
          frequency: string
          conditions: string | null
          trend: 'increasing' | 'decreasing'
          change_percentage: number
          competitor: string | null
          user_id: string
        }
        Insert: {
          id?: number
          alert_id: number
          timestamp?: string
          message: string
          alert_type: string
          value: number
          threshold: number
          severity: 'low' | 'medium' | 'high'
          status: 'new' | 'read'
          metric?: string | null
          frequency: string
          conditions?: string | null
          trend: 'increasing' | 'decreasing'
          change_percentage: number
          competitor?: string | null
          user_id: string
        }
        Update: {
          id?: number
          alert_id?: number
          timestamp?: string
          message?: string
          alert_type?: string
          value?: number
          threshold?: number
          severity?: 'low' | 'medium' | 'high'
          status?: 'new' | 'read'
          metric?: string | null
          frequency?: string
          conditions?: string | null
          trend?: 'increasing' | 'decreasing'
          change_percentage?: number
          competitor?: string | null
          user_id?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
