'use client';

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react';

import {
  ClientSideRowModelModule,
  ColDef,
  GridApi,
  GridReadyEvent,
  ICellRendererParams,
  ModuleRegistry,
  ValueFormatterParams,
  iconSetQuartzLight,
  themeQuartz,
} from 'ag-grid-community';
import { AgGridReact } from 'ag-grid-react';
import { Download, RefreshCw, Search } from 'lucide-react';

import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Input } from '@kit/ui/input';
import { LoadingOverlay } from '@kit/ui/loading-overlay';
import { Trans } from '@kit/ui/trans';

import 'ag-grid-community/styles/ag-theme-quartz.css';

// Register the required modules
ModuleRegistry.registerModules([ClientSideRowModelModule]);

interface UniqueFeature {
  feature: string;
  reasons: string[];
  uniqueness_score: number;
}

interface PricingMetrics {
  avg_price: number;
  has_free_plan: boolean;
  has_trial: boolean;
  price_range: number;
  pricing_competitiveness: number;
}

interface PricingAnalysis {
  avg_price: number;
  competitive_position: string;
  has_free_plan: boolean;
  has_trial: boolean;
  median_price: number;
  price_range_max: number;
  price_range_min: number;
  price_trend: string;
  pricing_model: string;
  pricing_strategy: string;
}

interface ReviewFrequency {
  reviews_per_day: number;
  reviews_per_month: number;
}

interface SeasonalPatterns {
  monthly_distribution: Record<string, number>;
  peak_months: number[];
}

interface Developer {
  name: string;
  location: string;
  launched_date: string;
  support_email: string;
  url: string;
}

interface Feature {
  items: string[];
  section_name: string;
}

interface PricingPlan {
  billing_frequency: string;
  features: string[];
  has_trial: boolean;
  is_free: boolean;
  plan_name: string;
  price: number;
  trial_days: number;
  yearly_discount_percentage: number;
  yearly_price: number;
}

interface RatingData {
  count: number;
  star_rating: number;
}

interface InsightData {
  app_id: string;
  app_name: string;
  url: string;
  developer_url: string;  // Add this
  built_for_shopify: boolean;  // Add this
  developer: Developer;
  features: Feature[];
  languages: string[];
  ratings_data: RatingData[];
  pricing_plans: {
    currency: string;
    plans: PricingPlan[];
    pricing_notes: string[];
  };
  analysis: {
    analysis_timestamp: string;
    market_position: string;
    performance_metrics: {
      pricing_metrics: PricingMetrics;
    };
    pricing_analysis: PricingAnalysis;
    market_analysis: {
      unique_value_props: {
        unique_features: UniqueFeature[];
      };
    };
    temporal_analysis: {
      review_frequency: ReviewFrequency;
      seasonal_patterns: SeasonalPatterns;
    };
  };
  works_with: string[];
  categories: string[];
}


export function InsightsGrid() {
  const [rowData, setRowData] = useState<InsightData[]>([]);
  const [loading, setLoading] = useState(true);
  const [gridApi, setGridApi] = useState<GridApi | null>(null);
  const [quickFilter, setQuickFilter] = useState('');

  const gridStyle = {
    height: 'calc(100vh - 8rem)',
    minHeight: '500px',
    width: '100%',
  } as React.CSSProperties;

  const ratingCellRenderer = (
    params: ICellRendererParams<InsightData, number>,
  ) => {
    const rating = params.value;
    if (!rating) return '<div class="text-gray-400">N/A</div>';
    const stars =
      '★'.repeat(Math.floor(rating)) + '☆'.repeat(5 - Math.floor(rating));
    const color =
      rating >= 4.5
        ? 'text-green-600'
        : rating >= 4.0
          ? 'text-blue-600'
          : rating >= 3.0
            ? 'text-yellow-600'
            : 'text-red-600';
    return `<div class="${color} font-semibold tracking-wide">${stars} (${rating})</div>`;
  };

  const marketPositionCellRenderer = (
    params: ICellRendererParams<InsightData, string>,
  ) => {
    const position = params.value || 'Unknown';
    const colorMap: Record<string, string> = {
      Leader: 'bg-green-50 text-green-700 border border-green-200',
      Challenger: 'bg-blue-50 text-blue-700 border border-blue-200',
      'Niche Player': 'bg-yellow-50 text-yellow-700 border border-yellow-200',
      Unknown: 'bg-gray-50 text-gray-700 border border-gray-200',
    };
    const color = colorMap[position] || colorMap.Unknown;
    return `<div class="px-3 py-1 rounded-full ${color} text-xs font-medium text-center">${position}</div>`;
  };

  const urlCellRenderer = (
    params: ICellRendererParams<InsightData, string>,
  ) => {
    const url = params.value;
    return url;
    if (!url) return '<div class="text-gray-400">N/A</div>';
    return `<a href="${url}" target="_blank" class="inline-flex items-center px-3 py-1 rounded-md bg-blue-50 text-blue-600 hover:bg-blue-100 transition-colors duration-150 text-sm font-medium">View App</a>`;
  };

  const columnDefs = useMemo<ColDef<InsightData>[]>(
    () => [
      {
        field: 'app_name',
        headerName: 'App Name',
        sortable: true,
        filter: true,
        flex: 2,
        pinned: 'left',
        cellClass: 'font-medium',
        headerClass: 'font-semibold',
      },
      {
        field: 'url',
        headerName: 'URL',
        sortable: true,
        filter: true,
        cellRenderer: urlCellRenderer,
        width: 120,
        valueFormatter: (params: ValueFormatterParams<InsightData, string>) => {
          const url = params.value;
          if (!url) return 'N/A';
          return `<a href="${url}" target="_blank" class="inline-flex items-center px-3 py-1 rounded-md bg-blue-50 text-blue-600 hover:bg-blue-100 transition-colors duration-150 text-sm font-medium">View App</a>`;
        },
      },
      {
        field: 'analysis.market_position',
        headerName: 'Market Position',
        sortable: true,
        filter: true,
        cellRenderer: marketPositionCellRenderer,
        width: 150,
        cellClass: 'flex items-center justify-center',
      },
      {
        field: 'analysis.performance_metrics.pricing_metrics.avg_price',
        headerName: 'Avg Price',
        sortable: true,
        filter: 'agNumberColumnFilter',
        valueFormatter: (params: ValueFormatterParams<InsightData, number>) =>
          params.value
            ? params.value.toLocaleString('en-US', {
                style: 'currency',
                currency: 'USD',
              })
            : 'N/A',
        width: 120,
        cellClass: 'font-mono',
      },
      {
        field: 'analysis.pricing_analysis.pricing_model',
        headerName: 'Pricing Model',
        sortable: true,
        filter: true,
        width: 140,
        cellClass: 'capitalize',
      },
      {
        field: 'analysis.pricing_analysis.price_trend',
        headerName: 'Price Trend',
        sortable: true,
        filter: true,
        width: 130,
        cellRenderer: (params: ICellRendererParams<InsightData, string>) => {
          const trend = params.value || 'Unknown';
          const colorMap: Record<string, string> = {
            Increasing: 'text-green-600',
            Decreasing: 'text-red-600',
            Stable: 'text-blue-600',
            Unknown: 'text-gray-600',
          };
          const color = colorMap[trend] || colorMap.Unknown;
          return `<div class="${color} font-medium">${trend}</div>`;
        },
      },
      {
        field: 'developer_url',
        headerName: 'Developer URL',
        sortable: true,
        filter: true,
        width: 200,
        cellRenderer: (params: ICellRendererParams<InsightData, string>) => {
          const url = params.value;
          return url
            ? `<a href="${url}" target="_blank" class="text-blue-600 underline">Link</a>`
            : '<div class="text-gray-400">N/A</div>';
        },
      },
      {
        field: 'built_for_shopify',
        headerName: 'Built for Shopify',
        sortable: true,
        filter: true,
        width: 160,
        cellRenderer: (params: ICellRendererParams<InsightData, boolean>) => {
          return params.value
            ? `<span class="text-green-600 font-semibold">Yes</span>`
            : `<span class="text-gray-500">No</span>`;
        },
      },
      {
        field: 'developer', // Make sure this key exists in your InsightData
        headerName: 'Developed By',
        sortable: true,
        filter: true,
        width: 200,
        cellRenderer: (params: ICellRendererParams<InsightData, string>) => {
          return params.value
            ? `<span class="text-gray-800">${params.value}</span>`
            : `<span class="text-gray-400">Unknown</span>`;
        },
      },
      
      
      // {
      //   field: 'analysis.temporal_analysis.review_frequency.reviews_per_month',
      //   headerName: 'Monthly Reviews',
      //   sortable: true,
      //   filter: 'agNumberColumnFilter',
      //   valueFormatter: (params: ValueFormatterParams<InsightData, number>) =>
      //     params.value?.toFixed(1) ?? '0',
      //   width: 140,
      //   cellClass: 'font-mono',
      // },
      {
        field: 'analysis.market_analysis.unique_value_props.unique_features',
        headerName: 'Unique Features',
        sortable: true,
        filter: true,
        valueFormatter: (
          params: ValueFormatterParams<InsightData, UniqueFeature[]>,
        ) => params.value?.map((f) => f.feature).join(', ') ?? '',
        width: 250,
        tooltipField:
          'analysis.market_analysis.unique_value_props.unique_features',
      },
      {
        field: 'developer.name',
        headerName: 'Developer',
        sortable: true,
        filter: true,
        width: 150,
      },
      {
        field: 'developer.location',
        headerName: 'Location',
        sortable: true,
        filter: true,
        width: 130,
      },
      {
        field: 'categories',
        headerName: 'Categories',
        sortable: true,
        filter: true,
        cellRenderer: (params: any) =>
          Array.isArray(params.value) && params.value.length > 0 ? (
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
              {params.value.map((cat: string) => (
                <span
                  key={cat}
                  style={{
                    background: '#dbeafe',
                    color: '#1e40af',
                    padding: '2px 8px',
                    borderRadius: '9999px',
                    fontSize: '12px',
                    marginRight: '4px',
                    marginBottom: '2px',
                    display: 'inline-block',
                  }}
                >
                  {cat}
                </span>
              ))}
            </div>
          ) : (
            <span style={{ color: '#9ca3af', fontSize: '12px' }}>N/A</span>
          ),
        width: 200,
      },
      {
        field: 'features',
        headerName: 'Features',
        sortable: true,
        filter: true,
        valueFormatter: (
          params: ValueFormatterParams<InsightData, Feature[]>,
        ) => params.value?.map((f) => f.section_name).join(', ') ?? '',
        width: 200,
        tooltipField: 'features',
      },
      {
        field: 'languages',
        headerName: 'Languages',
        sortable: true,
        filter: true,
        valueFormatter: (params: ValueFormatterParams<InsightData, string[]>) =>
          params.value?.join(', ') ?? '',
        width: 150,
      },
      {
        field: 'pricing_plans.plans',
        headerName: 'Pricing Plans',
        sortable: true,
        filter: true,
        valueFormatter: (
          params: ValueFormatterParams<InsightData, PricingPlan[]>,
        ) =>
          params.value?.map((p) => `${p.plan_name} ($${p.price})`).join(', ') ??
          '',
        width: 250,
        tooltipField: 'pricing_plans.plans',
      },
      {
        field: 'ratings_data',
        headerName: 'Ratings',
        sortable: true,
        cellRenderer: ratingCellRenderer,
        valueFormatter: (
          params: ValueFormatterParams<InsightData, RatingData[]>,
        ) => {
          if (!params.value?.length) return 'N/A';
          const total = params.value.reduce((sum, r) => sum + r.count, 0);
          const avgRating =
            params.value.reduce((sum, r) => sum + r.star_rating * r.count, 0) /
            total;
          return avgRating ? avgRating.toFixed(2) : 'N/A';
        },
        width: 120,
      },
      {
        field: 'works_with',
        headerName: 'Works With',
        sortable: true,
        filter: true,
        valueFormatter: (params: ValueFormatterParams<InsightData, string[]>) =>
          params.value?.join(', ') ?? '',
        width: 200,
      },
      {
        field: 'analysis.analysis_timestamp',
        headerName: 'Analyzed At',
        sortable: true,
        filter: 'agDateColumnFilter',
        valueFormatter: (params: ValueFormatterParams<InsightData, string>) =>
          params.value ? new Date(params.value).toLocaleString() : 'N/A',
        width: 180,
        cellClass: 'font-mono text-gray-600',
      },
    ],
    [],
  );

  const defaultColDef = useMemo<ColDef<InsightData>>(
    () => ({
      sortable: true,
      filter: true,
      resizable: true,
      suppressMovable: false,
      cellClass: 'text-sm',
      headerClass: 'text-sm font-semibold',
      filterParams: {
        buttons: ['reset', 'apply'],
        closeOnApply: true,
      },
    }),
    [],
  );

  const onGridReady = useCallback((params: GridReadyEvent) => {
    setGridApi(params.api);
    // params.api.sizeColumnsToFit();
  }, []);

  const onQuickFilterChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setQuickFilter(e.target.value);
      if (gridApi) {
        (gridApi as any).setQuickFilter(e.target.value);
      }
    },
    [gridApi],
  );

  const exportData = useCallback(() => {
    gridApi?.exportDataAsCsv({
      fileName: `insights-${new Date().toISOString().split('T')[0]}.csv`,
    });
  }, [gridApi]);

  useEffect(() => {
    fetchInsightsData();
    // const handleResize = () => {
    //   if (gridApi) {
    //     gridApi.sizeColumnsToFit();
    //   }
    // };
    // window.addEventListener('resize', handleResize);
    // return () => window.removeEventListener('resize', handleResize);
  }, []);

  // useEffect(() => {
  //   if (gridApi) {
  //     gridApi.sizeColumnsToFit();
  //   }
  // }, [gridApi]);

  const fetchInsightsData = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/insights');
      if (!response.ok) throw new Error('Failed to fetch data');
      const data = await response.json();
      setRowData(data);
    } catch (error) {
      // Error handling - consider adding user-visible error state
    } finally {
      setLoading(false);
    }
  };

  // if (loading) return <LoadingOverlay />;

  return (
    <div className="h-screen w-full p-4">
      {loading ? (
        <>
          {/* Add a loader here */}
          Loading...
        </>
      ) : (
        <Card className="h-full w-full">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
            <CardTitle className="text-2xl font-semibold text-gray-900">
              <Trans i18nKey={'insights:title'} />
            </CardTitle>
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                <Input
                  type="text"
                  placeholder="Quick filter..."
                  value={quickFilter}
                  onChange={onQuickFilterChange}
                  className="h-10 w-64 border-gray-200 bg-white pl-10 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
              <Button
                onClick={exportData}
                variant="outline"
                size="sm"
                className="inline-flex h-10 items-center gap-2 bg-white px-4 hover:bg-gray-50"
              >
                <Download className="h-4 w-4" />
                Export CSV
              </Button>
              <Button
                onClick={fetchInsightsData}
                variant="outline"
                size="sm"
                className="inline-flex h-10 items-center gap-2 bg-white px-4 hover:bg-gray-50"
              >
                <RefreshCw className="h-4 w-4" />
                Refresh
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="ag-theme-quartz" style={gridStyle}>
              <AgGridReact<InsightData>
                modules={[ClientSideRowModelModule]}
                rowData={rowData}
                columnDefs={columnDefs}
                defaultColDef={defaultColDef}
                theme={themeQuartz}
                pagination={true}
                paginationPageSize={25}
                onGridReady={onGridReady}
                animateRows={true}
                enableCellTextSelection={true}
                rowSelection="multiple"
                suppressRowClickSelection={true}
                suppressCopyRowsToClipboard={false}
                rowHeight={48}
                headerHeight={48}
                suppressDragLeaveHidesColumns={true}
                suppressMakeColumnVisibleAfterUnGroup={true}
                domLayout="normal"
              />
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}