import React, { useMemo } from 'react';
import { Bar<PERSON>hart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer } from 'recharts';

const ReviewsBarChartRenderer = (params) => {
  const monthlyData = useMemo(() => {
    if (!params.value || !Array.isArray(params.value)) return [];
    
    // Create a map to store review counts by month
    const monthCounts = {};
    
    params.value.forEach(review => {
      if (review.review_date && review.review_date.review_date) {
        const date = new Date(review.review_date.review_date);
        if (!isNaN(date.getTime())) {
          const monthYear = date.toLocaleString('default', { month: 'short', year: 'numeric' });
          monthCounts[monthYear] = (monthCounts[monthYear] || 0) + 1;
        }
      }
    });
    
    // Convert to array and sort by date
    const sortedData = Object.entries(monthCounts)
      .map(([month, count]) => ({
        month,
        count,
        date: new Date(month)
      }))
      .sort((a, b) => b.date - a.date) // Sort in descending order
      .slice(0, 6) // Take only the last 6 months
      .reverse(); // Reverse to show oldest to newest
    
    // Calculate the maximum count for relative scaling
    const maxCount = Math.max(...sortedData.map(item => item.count));
    
    // Add percentage for gradient calculation
    return sortedData.map(item => ({
      month: item.month,
      count: item.count,
      percentage: ((item.count / maxCount) * 100).toFixed(1)
    }));
  }, [params.value]);

  if (monthlyData.length === 0) {
    return <div className="flex items-center justify-center h-full text-gray-500">No review data available</div>;
  }

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white border border-gray-200 shadow-lg rounded-lg p-3 text-sm">
          <p className="font-semibold text-gray-900 mb-1">{label}</p>
          <div className="space-y-1">
            <p className="text-blue-600 font-medium">
              {`${payload[0].value} reviews`}
            </p>
            <p className="text-gray-600">
              {`${data.percentage}% of peak month`}
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  const CustomBar = (props) => {
    const { x, y, width, height, percentage } = props;
    return (
      <g>
        <defs>
          <linearGradient id={`gradient-${x}-${y}`} x1="0" y1="0" x2="0" y2="1">
            <stop offset="0%" stopColor="#3b82f6" stopOpacity={0.9} />
            <stop offset="100%" stopColor="#60a5fa" stopOpacity={0.4} />
          </linearGradient>
        </defs>
        <rect
          x={x}
          y={y}
          width={width}
          height={height}
          fill={`url(#gradient-${x}-${y})`}
          rx={4}
          ry={4}
          className="transition-all duration-300 hover:opacity-80"
        />
      </g>
    );
  };

  return (
    <div className="w-full h-full p-2">
      <ResponsiveContainer width="100%" height={120}>
        <BarChart 
          data={monthlyData}
          margin={{ top: 10, right: 10, bottom: 20, left: 25 }}
          barSize={30}
        >
          <XAxis 
            dataKey="month" 
            tick={{ fontSize: 11, fill: '#6b7280' }}
            axisLine={{ stroke: '#e5e7eb' }}
            tickLine={{ stroke: '#e5e7eb' }}
            interval={0}
          />
          <YAxis 
            tick={{ fontSize: 11, fill: '#6b7280' }}
            axisLine={{ stroke: '#e5e7eb' }}
            tickLine={{ stroke: '#e5e7eb' }}
            width={35}
          />
          <Tooltip 
            content={<CustomTooltip />}
            cursor={{ fill: 'rgba(59, 130, 246, 0.1)' }}
          />
          <Bar 
            dataKey="count" 
            shape={<CustomBar />}
            radius={[4, 4, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default ReviewsBarChartRenderer;