'use client';
import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react';
import React from 'react';
import { FaStar, FaStarHalfAlt, FaRegStar } from 'react-icons/fa';
import { useInsights } from '~/hooks/use-insights';
import { InsightsGridSkeleton } from '~/components/skeletons/grid-skeleton';
import {
  ClientSideRowModelModule,
  ColDef,
  GridApi,
  GridReadyEvent,
  ICellRendererParams,
  ValueFormatterParams,
  iconSetQuartzLight,
  themeQuartz,
  NumberEditorModule,
  NumberFilterModule,
  PaginationModule,
  RowSelectionModule,
  TextEditorModule,
  TextFilterModule,
  ValidationModule,
  QuickFilterModule,
  RowAutoHeightModule,
} from 'ag-grid-community';
// import {
//   ColumnMenuModule,
//   ColumnsToolPanelModule,
//   ContextMenuModule,
//   RowGroupingModule,
// } from "ag-grid-enterprise";
import { AgGridReact } from 'ag-grid-react';
import { Download, RefreshCw, Search } from 'lucide-react';
import ReviewsBarChartRenderer from './ReviewsBarChartRenderer';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Input } from '@kit/ui/input';
import { ExternalLink as ExternalLinkIcon } from 'lucide-react';
import { LoadingOverlay } from '@kit/ui/loading-overlay';
import { Trans } from '@kit/ui/trans';
import { Tooltip } from '@kit/ui/tooltip';
// import 'ag-grid-community/styles/ag-theme-quartz.css';
import '~/styles/ag-grid-custom.css';
import { useTheme } from 'next-themes';
import { colorSchemeDark } from 'ag-grid-community';

interface ReviewData {
  review_id: string;
  reviewer_name: string;
  review_rating: number;
  review_comment: string;
  review_date: {
    review_day: string;
    review_month: number;
    review_year: number;
    review_date: string;
  };
  reviewer_country: string;
  review_usage: {
    value: number;
    type: string; // Could be "Days", "Months", or "Years"
  };
  review_response: string | null;
  response_date: {
    day: number;
    month: number;
    year: number;
    date: string;
  } | null;
  response_responder: string | null;
}
interface PricingPlan {
  plan_id: string;
  plan_name: string;
  billing_frequency: string;
  currency: string;
  pricing_component: {
    monthly_price: number;
    yearly_price: number;
    savings_percentage: number;
  };
  features: string[];
  trial_days: {
    value: number;
    type: string;
  };
  has_trial: boolean;
  is_free: boolean;
}
interface ScreenshotData {
  screenshot_id: string;
  image_type: string;
  screenshot_url: string;
  height: number[];
  width: number[];
}
interface InsightData {
  app_id: string;
  app_name: string;
  app_slug: string;
  url: string;
  video_url: string;
  short_description: string[];
  long_description: string[];
  highlights: string[];
  languages: string[];
  works_with: string[];
  categories: string[];
  developer: string;
  developer_url: string;
  built_for_shopify: boolean;
  rating: number;
  review_count: number;
  pricing_plans: PricingPlan[];
  reviews: ReviewData[];
  screenshots: ScreenshotData[];
}

// Custom cell renderer for star ratings
const StarRatingRenderer = (params: ICellRendererParams) => {
  const rating = params.value;
  if (!rating) return 'None';

  const maxStars = 5;
  const stars = [];

  for (let i = 1; i <= maxStars; i++) {
    if (i <= rating) {
      stars.push(<FaStar key={i} color="#ffc107" style={{ marginRight: 2 }} />);
    } else if (i - 0.5 <= rating) {
      stars.push(<FaStarHalfAlt key={i} color="#ffc107" style={{ marginRight: 2 }} />);
    } else {
      stars.push(<FaRegStar key={i} color="#e4e5e9" style={{ marginRight: 2 }} />);
    }
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'row', paddingTop: '13px' }}>
      {stars}
    </div>
  );
};

const HighlightsRenderer = (params: ICellRendererParams<InsightData, string[]>) => {
  const highlights = params.value;
  if (!highlights || highlights.length === 0) return 'None';

  return (
    <div>
      {highlights.map((highlight, index) => (
        <div key={index} className="mb-1">
          {highlight}{index === highlights.length - 1 ? '' : ','}
        </div>
      ))}
    </div>
  );
};

export function InsightsGrid() {
  // Use React Query hook for data fetching
  const {
    data,
    isLoading: loading,
    error: queryError,
    isError
  } = useInsights();

  // Extract insights data with fallback
  const rowData = data?.insights || [];

  const [gridApi, setGridApi] = useState<GridApi | null>(null);
  const [quickFilter, setQuickFilter] = useState('');
  const { theme } = useTheme();

  const agGridTheme = useMemo(() => {
    if (theme === 'dark') {
      return themeQuartz.withPart(colorSchemeDark);
    }
    return themeQuartz;
  }, [theme]);

  const gridStyle = {
    height: 'calc(100vh - 8rem)',
    minHeight: '500px',
    width: '100%',
  } as React.CSSProperties;

  const urlCellRenderer = (params: ICellRendererParams<InsightData, string>) => {
    const url = params.value;
  
    if (!url) {
      return; // In case URL is missing, show None
    }
  
    return (
      <a
        href={url}
        target="_blank"
        rel="noopener noreferrer"
        className="inline-flex items-center px-3 py-1 rounded-md bg-accent text-accent-foreground hover:bg-accent/80 transition-colors duration-150 text-sm font-medium"
      >
        View app
      </a>
    );
  };

  const AppNameWithIconRenderer = (params: ICellRendererParams<InsightData>) => {
    const appName = params.data?.app_name;
    const url = params.data?.url;
  
    return (
      <div className="flex items-center gap-2">
        <span>{appName}</span>
        {url && (
          <a
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            className="text-accent-foreground hover:text-accent/80"
          >
            <ExternalLinkIcon className="h-4 w-4" /> {/* Use an external link icon */}
          </a>
        )}
      </div>
    );
  };
  
  
  const columnDefs = useMemo<ColDef<InsightData>[]>(
    () => [
      {
        field: 'app_name',
        headerName: 'App Name',
        sortable: true,
        filter: true,
        flex: 2,
        pinned: 'left',
        cellClass: 'font-medium',
        headerClass: 'font-semibold',
        cellRenderer: AppNameWithIconRenderer, // Use the custom cell renderer
      },
      {
        field: 'url',
        headerName: 'URL',
        sortable: true,
        filter: true,
        cellRenderer: urlCellRenderer,
        width: 120,
        valueFormatter: (params: ValueFormatterParams<InsightData, string>) => {
          const url = params.value;
          if (!url) return 'None';
          return `<a href="${url}" target="_blank" class="inline-flex items-center px-3 py-1 rounded-md bg-accent text-accent-foreground hover:bg-accent/80 transition-colors duration-150 text-sm font-medium">View App</a>`;
        },
      },
     
      {
        field: 'app_slug',
        headerName: 'Slug',
        sortable: true,
        filter: true,
        width: 200,
        cellClass: 'flex items-center justify-center',
      },
      {
        field: 'highlights',
        headerName: 'Highlights',
        sortable: true,
        filter: true,
        width: 250,
        wrapText: true,
        autoHeight: true,
        cellRenderer: HighlightsRenderer,
      },
      {
        field: 'short_description',
        headerName: 'Short Description',
        sortable: true,
        filter: true,
        width: 500,
        cellClass: 'flex items-center justify-center',
      },
      {
        field: 'long_description',
        headerName: 'Long Description',
        sortable: true,
        filter: true,
        width: 700,
        
        cellClass: 'flex items-center justify-center',
      },
      {
        field: 'languages',
        headerName: 'Languages',
        sortable: true,
        filter: true,
        valueFormatter: (params: ValueFormatterParams<InsightData, string[]>) =>
          params.value?.join(', ') ?? '',
        width: 250,
        cellClass: 'flex items-center justify-center',
      },
      {
        field: 'developer',
        headerName: 'Developer',
        sortable: true,
        filter: true,
        width: 180,
        cellClass: 'flex items-center justify-start',
      },
      {
        field: 'developer_url',
        headerName: 'Developer Website',
        sortable: true,
        filter: true,
        width: 200,
        cellRenderer: (params: ICellRendererParams<InsightData, string>) => {
          const url = params.value;
          if (!url) return 'None';
          return (
            <a
              href={url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-accent-foreground hover:text-accent/80 text-sm"
            >
              Visit Site
            </a>
          );
        },
      },
      {
        field: 'built_for_shopify',
        headerName: 'Built for Shopify',
        sortable: true,
        filter: true,
        width: 150,
        cellRenderer: (params: ICellRendererParams<InsightData, boolean>) => {
          return params.value ? (
            <span className="text-green-500 font-medium">Yes</span>
          ) : (
            <span className="text-muted-foreground">No</span>
          );
        },
      },
      
      {
        field: 'categories',
        headerName: 'Categories',
        sortable: true,
        filter: true,
        cellRenderer: (params: ICellRendererParams<InsightData, string[]>) => {
          if (!params.value || params.value.length === 0) return 'None';
          return (
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
              {params.value.map((cat: string) => (
                <span
                  key={cat}
                  className="bg-secondary text-secondary-foreground px-2 py-0.5 rounded-full text-xs"
                >
                  {cat}
                </span>
              ))}
            </div>
          );
        },
        width: 200,
      },
      // {
      //   field: 'reviews',
      //   headerName: 'Monthly Reviews',
      //   sortable: false,
      //   filter: false,
      //   cellRenderer: ReviewsBarChartRenderer,
      //   width: 425,
      //   cellStyle: { padding: '5px' },
      // },
      {
        field: 'rating',
        headerName: 'Ratings',
        sortable: true,
        width: 120,
        cellRenderer: StarRatingRenderer
      },
      {
        field: 'review_count',
        headerName: 'Review count',
        sortable: true,
        width: 120,
      },
      {
        field: 'works_with',
        headerName: 'Works With',
        sortable: true,
        filter: true,
        valueFormatter: (params: ValueFormatterParams<InsightData, string[]>) =>
          params.value?.join('\n') ?? '',
        width: 200,
      },
      {
        field: 'pricing_plans',
        headerName: 'Plan Name',
        sortable: true,
        filter: true,
        width: 200,
        cellRenderer: (params: ICellRendererParams<InsightData, PricingPlan[]>) => {
          if (!params.value || params.value.length === 0) return 'None';
          return (
            <div>
              {params.value.map((plan, index) => (
                <div key={index}>{plan.plan_name}</div>
              ))}
            </div>
          );
        },
      },
      {
        field: 'pricing_plans',
        headerName: ' Billing Frequency',
        sortable: true,
        filter: true,
        width: 200,
        cellRenderer: (params: ICellRendererParams<InsightData, PricingPlan[]>) => {
          if (!params.value || params.value.length === 0) return 'None';
          return (
            <div>
              {params.value.map((plan, index) => (
                <div key={index}>{plan.billing_frequency}</div>
              ))}
            </div>
          );
        },
      },
      {
        field: 'pricing_plans',
        headerName: 'Features',
        sortable: true,
        filter: true,
        width: 600,
        cellRenderer: (params: ICellRendererParams<InsightData, PricingPlan[]>) => {
          if (!params.value || params.value.length === 0) return 'None';
          return (
            <div>
              {params.value.map((plan, index) => (
                <div key={index}>{plan.features.join(', ')}</div>
              ))}
            </div>
          );
        },
      },
      {
        field: 'pricing_plans',
        headerName: 'Monthly Price',
        sortable: true,
        filter: true,
        width: 150,
        cellRenderer: (params: ICellRendererParams<InsightData, PricingPlan[]>) => {
          const plans = params.value;
          if (!plans || plans.length === 0) return 'None';
        
          return (
            <div>
              {plans.map((plan, index) => {
                const name = plan.plan_name || 'Unnamed';
                const price = plan.pricing_component?.monthly_price;
                if (plan.is_free) return <div key={index}>{`${name} (Free)`}</div>;
                if (typeof price === 'number') return <div key={index}>{`${name} ($${price})`}</div>;
                return <div key={index}>{`${name} (None)`}</div>;
              })}
            </div>
          );
        }
      },
      {
        field: 'pricing_plans',
        headerName: 'Yearly Price',
        sortable: true,
        filter: true,
        width: 150,
        cellRenderer: (params: ICellRendererParams<InsightData, PricingPlan[]>) => {
          if (!params.value || params.value.length === 0) return 'None';
        
          return (
            <div>
              {params.value.map((plan, index) => {
                const price = plan.pricing_component?.yearly_price;
                return <div key={index}>{typeof price === 'number' ? `$${price}` : 'None'}</div>;
              })}
            </div>
          );
        },
      },
      {
        field: 'pricing_plans',
        headerName: 'Savings Percentage',
        sortable: true,
        filter: true,
        width: 150,
        cellRenderer: (params: ICellRendererParams<InsightData, PricingPlan[]>) => {
          if (!params.value || params.value.length === 0) return 'None';
        
          return (
            <div>
              {params.value.map((plan, index) => {
                const savings = plan.pricing_component?.savings_percentage;
                return <div key={index}>{typeof savings === 'number' ? `${savings}%` : 'None'}</div>;
              })}
            </div>
          );
        },
      },
      {
        field: 'pricing_plans',
        headerName: 'Has Trial',
        sortable: true,
        filter: true,
        width: 120,
        valueFormatter: (params: ValueFormatterParams<InsightData, PricingPlan[]>) =>
          params.value?.some((plan) => plan.has_trial) ? 'Yes' : 'No',
      },
      {
        field: 'pricing_plans',
        headerName: 'Is Free',
        sortable: true,
        filter: true,
        width: 120,
        valueFormatter: (params: ValueFormatterParams<InsightData, PricingPlan[]>) =>
          params.value?.some((plan) => plan.is_free) ? 'Yes' : 'No',
      },
      {
        field: 'pricing_plans',
        headerName: 'Trial Days',
        sortable: true,
        filter: true,
        width: 120,
        cellRenderer: (params: ICellRendererParams<InsightData, PricingPlan[]>) => {
          if (!params.value || params.value.length === 0) return 'None';
        
          return (
            <div>
              {params.value.map((plan, index) => (
                <div key={index}>{plan.trial_days ? plan.trial_days.value : 'None'}</div>
              ))}
            </div>
          );
        },
      },
      // {
      //   field: 'reviews',
      //   headerName: 'Reviewer Name',
      //   sortable: true,
      //   filter: true,
      //   width: 150,
      //   cellRenderer: (params: ICellRendererParams<InsightData, ReviewData[]>) => {
      //     if (!params.value || params.value.length === 0) return 'None';
      //     return (
      //       <div>
      //         {params.value.map((review, index) => (
      //           <div key={index}>{review.reviewer_name}</div>
      //         ))}
      //       </div>
      //     );
      //   },
      // },
      // {
      //   field: 'reviews',
      //   headerName: 'Review Rating',
      //   sortable: true,
      //   filter: true,
      //   width: 120,
      //   cellRenderer: (params: ICellRendererParams<InsightData, ReviewData[]>) => {
      //     if (!params.value || params.value.length === 0) return 'None';
      //     return (
      //       <div>
      //         {params.value.map((review, index) => (
      //           <div key={index}>{review.review_rating}</div>
      //         ))}
      //       </div>
      //     );
      //   },
      // },
      // {
      //   field: 'reviews',
      //   headerName: 'Review Comment',
      //   sortable: true,
      //   filter: true,
      //   width: 200,
      //   cellRenderer: (params: ICellRendererParams<InsightData, ReviewData[]>) => {
      //     if (!params.value || params.value.length === 0) return 'None';
      //     return (
      //       <div>
      //         {params.value.map((review, index) => (
      //           <div key={index}>{review.review_comment}</div>
      //         ))}
      //       </div>
      //     );
      //   },
     // },
      // {
      //   field: 'reviews',
      //   headerName: 'Review Date',
      //   sortable: true,
      //   filter: true,
      //   width: 150,
      //   cellRenderer: (params: ICellRendererParams<InsightData, ReviewData[]>) => {
      //     if (!params.value || params.value.length === 0) return 'None';
      //     return (
      //       <div>
      //         {params.value.map((review, index) => (
      //           <div key={index}>{review.review_date.review_date}</div>
      //         ))}
      //       </div>
      //     );
      //   },
      // },
      // {
      //   field: 'reviews',
      //   headerName: 'Reviewer Country',
      //   sortable: true,
      //   filter: true,
      //   width: 150,
      //   cellRenderer: (params: ICellRendererParams<InsightData, ReviewData[]>) => {
      //     if (!params.value || params.value.length === 0) return 'None';
      //     return (
      //       <div>
      //         {params.value.map((review, index) => (
      //           <div key={index}>{review.reviewer_country}</div>
      //         ))}
      //       </div>
      //     );
      //   },
      // },
      // {
      //   field: 'reviews',
      //   headerName: 'Review Usage',
      //   sortable: true,
      //   filter: true,
      //   width: 150,
      //   cellRenderer: (params: ICellRendererParams<InsightData, ReviewData[]>) => {
      //     if (!params.value || params.value.length === 0) return 'None';
      //     return (
      //       <div>
      //         {params.value.map((review, index) => (
      //           <div key={index}>
      //             {review.review_usage.value} {review.review_usage.type}
      //           </div>
      //         ))}
      //       </div>
      //     );
      //   },
      // },
      // {
      //   field: 'reviews',
      //   headerName: 'Review Response',
      //   sortable: true,
      //   filter: true,
      //   width: 200,
      //   cellRenderer: (params: ICellRendererParams<InsightData, ReviewData[]>) => {
      //     if (!params.value || params.value.length === 0) return 'None';
      //     return (
      //       <div>
      //         {params.value.map((review, index) => (
      //           <div key={index}>{review.review_response || 'No Response'}</div>
      //         ))}
      //       </div>
      //     );
      //   },
      // },
      // {
      //   field: 'reviews',
      //   headerName: 'Review Date',
      //   sortable: true,
      //   filter: true,
      //   width: 150,
      //   cellRenderer: (params: ICellRendererParams<InsightData, ReviewData[]>) => {
      //     console.log("Review Data:", params.value);
      //     if (!params.value || params.value.length === 0) return 'None';
      
      //     return (
      //       <div>
      //         {params.value.map((review, index) => {
      //           const reviewDate = review?.review_date;
      
      //           if (reviewDate) {
      //             let formattedDate = "Invalid Date";
      
      //             // If 'date' exists, format it properly
      //             if (reviewDate.review_date) {
      //               const dateObj = new Date(reviewDate.review_date);
      //               formattedDate = isNaN(dateObj.getTime()) 
      //                 ? "Invalid Date" 
      //                 : dateObj.toLocaleDateString();
      //             }
      //             // Fallback: Use day-month-year if available
      //             else if (reviewDate.review_date && reviewDate.review_month && reviewDate.review_year) {
      //               formattedDate = `${reviewDate.review_date}-${reviewDate.review_month}-${reviewDate.review_year}`;
      //             }
      
      //             return <div key={index}>{formattedDate}</div>;
      //           }
      //           return <div key={index}>No Date</div>;
      //         })}
      //       </div>
      //     );
      //   },
      // },
           
      // {
      //   field: 'reviews',
      //   headerName: 'Response Responder',
      //   sortable: true,
      //   filter: true,
      //   width: 150,
      //   cellRenderer: (params: ICellRendererParams<InsightData, ReviewData[]>) => {
      //     if (!params.value || params.value.length === 0) return 'None';
      //     return (
      //       <div>
      //         {params.value.map((review, index) => (
      //           <div key={index}>
      //             {review.response_responder || 'No Responder'}
      //           </div>
      //         ))}
      //       </div>
      //     );
      //   },
      // },
      {
        field: 'video_url',
        headerName: 'Video URL',
        sortable: true,
        filter: true,
        width: 150,
        cellRenderer: (params: ICellRendererParams<InsightData, string>) => {
          const url = params.value;
          if (!url) return 'None';
      
          return (
            <a
              href={url}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-3 py-1 rounded-md bg-accent text-accent-foreground hover:bg-accent/80 transition-colors duration-150 text-sm font-medium"
            >
              View Video
            </a>
          );
        },
      },
      // {
      //   field: 'screenshots',
      //   headerName: 'Image Type',
      //   sortable: true,
      //   filter: true,
      //   width: 150,
      //   cellRenderer: (params: ICellRendererParams<InsightData, ScreenshotData[]>) => {
      //     if (!params.value || params.value.length === 0) return 'None';
        
      //     return (
      //       <div>
      //         {params.value.map((screenshot, index) => (
      //           <div key={index}>{screenshot.image_type || 'None'}</div>
      //         ))}
      //       </div>
      //     );
      //   },
      // },
      {
        field: 'screenshots',
        headerName: 'Screenshots',
        sortable: true,
        filter: true,
        width: 150,
        cellRenderer: (params: ICellRendererParams<InsightData, ScreenshotData[]>) => {
          if (!params.value || params.value.length === 0) return 'None';
      
          return (
            <div className="flex flex-wrap gap-1 items-center py-1">
              {params.value.map((screenshot, index) => (
                <a
                  key={index}
                  href={screenshot.screenshot_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center justify-center px-2 py-1 rounded-md bg-accent text-accent-foreground hover:bg-accent/80 transition-colors duration-150 text-sm font-medium"
                >
                  {index + 1}
                </a>
              ))}
            </div>
          );
        },
      },
      // {
      //   field: 'screenshots',
      //   headerName: 'Height',
      //   sortable: true,
      //   filter: true,
      //   width: 200,
      //   cellRenderer: (params: ICellRendererParams<InsightData, ScreenshotData[]>) => {
      //     if (!params.value || params.value.length === 0) return 'None';
      
      //     const formatted = params.value
      //       .map(screenshot => {
      //         const heightArray = Array.isArray(screenshot.height)
      //           ? screenshot.height
      //           : [screenshot.height];
      //         return `[${heightArray.join(', ')}]`;
      //       })
      //       .join(', ');
      
      //     return <span>{formatted}</span>;
      //   },
      // },
      // {
      //   field: 'screenshots',
      //   headerName: 'Width',
      //   sortable: true,
      //   filter: true,
      //   width: 200,
      //   cellRenderer: (params: ICellRendererParams<InsightData, ScreenshotData[]>) => {
      //     if (!params.value || params.value.length === 0) return 'None';
      
      //     const formatted = params.value
      //       .map(screenshot => {
      //         const widthArray = Array.isArray(screenshot.width)
      //           ? screenshot.width
      //           : [screenshot.width];
      //         return `[${widthArray.join(', ')}]`;
      //       })
      //       .join(', ');
      
      //     return <span>{formatted}</span>;
      //   },
      // },
    ],
    [],
  );

  const defaultColDef = useMemo<ColDef<InsightData>>(
    () => ({
      sortable: true,
      filter: true,
      resizable: true,
      suppressMovable: false,
      wrapText: true,
      autoHeight: true,
      cellClass: 'text-sm p-2',
      headerClass: 'text-sm font-semibold',
      filterParams: {
        buttons: ['reset', 'apply'],
        closeOnApply: true,
      },
    }),
    [],
  );


  const onQuickFilterChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setQuickFilter(e.target.value);
      if (gridApi) {
        (gridApi as any).setQuickFilter(e.target.value);
      }
    },
    [gridApi],
  );

  const exportData = useCallback(() => {
    gridApi?.exportDataAsCsv({
      fileName: `insights-${new Date().toISOString().split('T')[0]}.csv`,
    });
  }, [gridApi]);

  // Data fetching now handled by React Query hook
  

  // Remove the old fetch function - now handled by React Query hook


  const necessaryColumns = [
    'app_name',
    'app_slug',
    'url',
    'video_url',
    'review_count',
    'highlights',
    'short_description',
    'long_description',
    'languages',
    'categories',
    'rating',

    // 'reviews', // This is for the monthly review graph
    'works_with',
    'pricing_plans',
    'screenshots',
    'developer',
    'developer_url',
    'built_for_shopify'  
  ];
  
  const filterColumns = (columns: ColDef<InsightData>[], necessaryColumns: string[]) => {
    return columns.filter((column) => necessaryColumns.includes(column.field as string));
  };

  const filteredColumnDefs = filterColumns(columnDefs, necessaryColumns);
  

  return (
    <div className="h-screen w-full p-4">
      {loading ? (
        <InsightsGridSkeleton />
      ) : (
        <Card className="h-full w-full">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
            <CardTitle className="text-2xl font-semibold text-gray-900 dark:text-white">App Data</CardTitle>
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                <Input
                  type="text"
                  placeholder="Quick filter..."
                  value={quickFilter}
                  onChange={onQuickFilterChange}
                  className="h-10 w-64 border-border bg-background pl-10 focus:border-primary focus:ring-primary text-foreground placeholder-foreground"
                />
              </div>
              <Button onClick={exportData} variant="outline" size="sm" className="inline-flex h-10 items-center gap-2 text-foreground bg-transparent border-border hover:bg-accent hover:text-accent-foreground">
                <Download className="h-4 w-4" />
                Export CSV
              </Button>
              <Button onClick={() => window.location.reload()} variant="outline" size="sm" className="inline-flex h-10 items-center gap-2 text-foreground bg-transparent border-border hover:bg-accent hover:text-accent-foreground">
                <RefreshCw className="h-4 w-4" />
                Refresh
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="ag-theme-quartz" style={gridStyle}>
              <AgGridReact<InsightData>
                modules={[
                  ClientSideRowModelModule,
                  QuickFilterModule,
                  NumberEditorModule,
                  TextEditorModule,
                  TextFilterModule,
                  NumberFilterModule,
                  RowSelectionModule,
                  PaginationModule,
                  ValidationModule,
                  RowAutoHeightModule,
                  // ColumnMenuModule,
                  // ColumnsToolPanelModule,
                  // ContextMenuModule,
                  // RowGroupingModule,
                ]}
                rowData={rowData}
                columnDefs={filteredColumnDefs}
                defaultColDef={defaultColDef}
                pagination={true}
                theme={agGridTheme}
                paginationPageSize={10}
                quickFilterText={quickFilter}
                onGridReady={(params) => params.api.sizeColumnsToFit()}
                animateRows={true}
                enableCellTextSelection={true}
                rowSelection="multiple"
                suppressRowClickSelection={true}
                rowHeight={160}
                headerHeight={60}
                domLayout="normal"
              />
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

