import { PageBody } from '@kit/ui/page';
import { Trans } from '@kit/ui/trans';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { withI18n } from '~/lib/i18n/with-i18n';
// local imports
import { HomeLayoutPageHeader } from './_components/home-page-header';
import { loadUserWorkspace } from './_lib/server/load-user-workspace';
import { use } from 'react';
import   Dashboard from './_components/dashboard';
import FeatureTiles from './_components/FeatureTile';
export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('account:homePage');

  return {
    title,
  };
};



function UserHomePage() {
      const workspace = use(loadUserWorkspace());
        // Check if the user is new based on the `created_at` timestamp
        const isNewUser = workspace?.user.created_at
        ? new Date().getTime() - new Date(workspace.user.created_at).getTime() < 24 * 60 * 60 * 1000
        : false;
      
  return (
    <>
    
    {isNewUser ? <FeatureTiles /> :<Dashboard/>}
    </>
  );
}
// function UserHomePage() {
//   const workspace = use(loadUserWorkspace());
  
//   // Check if the user is new based on the `created_at` timestamp (logic retained but not used now)
//   const isNewUser = workspace?.user.created_at
//     ? new Date().getTime() - new Date(workspace.user.created_at).getTime() < 24 * 60 * 60 * 1000
//     : false;

//   return (
//     <>
//       {/* Only FeatureTiles will be rendered */}
//       <FeatureTiles />
//     </>
//   );
// }



export default withI18n(UserHomePage);
