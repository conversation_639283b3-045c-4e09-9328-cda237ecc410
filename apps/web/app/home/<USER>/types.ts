export interface Alert {
    id: number;
    alertType: string;
    threshold: number;
    frequency: string;
    message?: string;
    metric?: string;
    conditions?: string;
    createdAt: Date;
    status: 'active' | 'paused';
    severity: 'low' | 'medium' | 'high';
  }
  
  export interface AlertNotification {
    id: number;
    alertId: number;
    timestamp: Date;
    message: string;
    alertType: string;
    value: number;
    threshold: number;
    severity: 'low' | 'medium' | 'high';
    status: 'new' | 'read';
    metadata: {
      metric: string;
      frequency: string;
      conditions?: string;
      trend: 'increasing' | 'decreasing';
      changePercentage: number;
      competitor?: string;
    };
  }