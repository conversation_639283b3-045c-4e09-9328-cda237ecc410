import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@kit/ui/card";
import Link from "next/link";
import { supabase } from "~/lib/supabase/client";

interface Category {
  category: string;
}

async function getCategories() {
  const { data: categories, error } = await supabase
    .from('analysis_reports')
    .select('category');

  if (error) {
    console.error('Error fetching categories:', error);
    return [];
  }

  // Get unique categories
  const uniqueCategories = Array.from(new Set(categories?.map(c => c.category) || []))
    .map(category => ({ category }));

  return uniqueCategories as Category[];
}

export default async function ReportsPage() {
  const categories = await getCategories();

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">Competitor Analysis Reports</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {categories.map((category: Category) => (
          <Link 
            href={`/home/<USER>/${encodeURIComponent(category.category)}`} 
            key={category.category}
            className="block"
          >
            <Card className="h-full hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle>{category.category}</CardTitle>
                <CardDescription>
                  View detailed analysis and evidence for {category.category}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Click to view comprehensive analysis, feature gaps, and supporting evidence
                </p>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  );
} 