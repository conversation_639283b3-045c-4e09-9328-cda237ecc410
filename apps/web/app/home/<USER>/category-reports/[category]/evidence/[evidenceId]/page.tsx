import { supabase } from "~/lib/supabase/client";
import { Card, CardContent, CardHeader, CardTitle } from "@kit/ui/card";
import { notFound } from "next/navigation";
import Link from "next/link";
import { ExternalLink } from "lucide-react";

async function getEvidenceDetails(evidenceId: string) {
  const { data: evidence, error } = await supabase
    .from('supporting_evidence')
    .select('original_review_comment, app_name, rating, app_slug, categories')
    .eq('id', evidenceId)
    .single();

  if (error) {
    console.error('Error fetching evidence details:', error);
    return null;
  }

  return evidence;
}

// Helper function to remove brackets from the review comment
const cleanReviewComment = (comment: string) => {
  if (!comment) return '';
  return comment.replace(/^\[|\]$/g, '');
};

export default async function EvidenceDetailPage({
  params,
}: {
  params: { category: string; evidenceId: string };
}) {
  const evidenceId = params.evidenceId;
  const evidence = await getEvidenceDetails(evidenceId);

  if (!evidence) {
    notFound();
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">Evidence Details</h1>
      <Card>
        <CardHeader>
          <CardTitle>Original Review</CardTitle>
        </CardHeader>
        <CardContent>
          <p>{cleanReviewComment(evidence.original_review_comment)}</p>
        </CardContent>
      </Card>

      <Card className="mt-4">
        <CardHeader>
          <CardTitle>App Name</CardTitle>
        </CardHeader>
        <CardContent className="flex items-center space-x-2">
          <p>{evidence.app_name}</p>
          <Link
            href={`https://apps.shopify.com/${evidence.app_slug}`}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-500 hover:underline"
          >
            <ExternalLink className="h-4 w-4" />
          </Link>
        </CardContent>
      </Card>

      <Card className="mt-4">
        <CardHeader>
          <CardTitle>User Rating</CardTitle>
        </CardHeader>
        <CardContent>
          <p>{evidence.rating} / 5</p>
        </CardContent>
      </Card>

      <Card className="mt-4">
        <CardHeader>
          <CardTitle>Categories</CardTitle>
        </CardHeader>
        <CardContent>
          <p>{evidence.categories?.join(', ') ?? 'None'}</p>
        </CardContent>
      </Card>
    </div>
  );
} 