import { supabase } from "~/lib/supabase/client";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@kit/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@kit/ui/card";
import { notFound } from "next/navigation";
import Link from "next/link";
import { Button } from "@kit/ui/button";

async function getCategoryData(category: string) {
  // Fetch analysis reports
  const { data: analysisReports, error: analysisError } = await supabase
    .from('analysis_reports')
    .select('*')
    .eq('category', category);

  if (analysisError) {
    console.error('Error fetching analysis reports:', analysisError);
    return null;
  }

  // Fetch feature gaps
  const { data: featureGaps, error: featureError } = await supabase
    .from('feature_gaps')
    .select('*')
    .eq('report_id', analysisReports?.[0]?.id);

  if (featureError) {
    console.error('Error fetching feature gaps:', featureError);
    return null;
  }

  // Fetch supporting evidence
  const { data: evidence, error: evidenceError } = await supabase
    .from('supporting_evidence')
    .select('*')
    .in('feature_gap_id', featureGaps?.map(gap => gap.id) || []);

  if (evidenceError) {
    console.error('Error fetching evidence:', evidenceError);
    return null;
  }

  return {
    analysisReports,
    featureGaps,
    evidence
  };
}

// Helper function for formatting comments
const formatReviewComment = (comment: string, maxLines: number = 3, maxLength: number = 200) => {
  if (!comment) return '';

  // Remove leading/trailing brackets
  let cleanedComment = comment.replace(/^\[|\]$/g, '');

  const lines = cleanedComment.split('\n');
  let truncatedComment = lines.slice(0, maxLines).join(' '); // Join first few lines

  // Always add ellipsis if the original comment is not empty
  const TRUNCATION_SUFFIX = '...';

  if (truncatedComment.length > maxLength) {
    truncatedComment = truncatedComment.substring(0, maxLength);
    const lastSpaceIndex = truncatedComment.lastIndexOf(' ');
    if (lastSpaceIndex > 0) {
      truncatedComment = truncatedComment.substring(0, lastSpaceIndex);
    }
    truncatedComment += TRUNCATION_SUFFIX;
  } else if (cleanedComment.length > 0) { // Add ellipsis even for small sentences if the comment is not empty
    truncatedComment += TRUNCATION_SUFFIX;
  }

  return truncatedComment;
};

export default async function CategoryPage({
  params,
}: {
  params: { category: string };
}) {
  const category = decodeURIComponent(params.category);
  const data = await getCategoryData(category);

  if (!data) {
    notFound();
  }

  const { analysisReports, featureGaps, evidence } = data;

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">{category} Analysis</h1>
      
      {/* Analysis Reports Section */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Analysis Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Analysis Date</TableHead>
                <TableHead>Created At</TableHead>
                <TableHead>Updated At</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {analysisReports.map((report) => (
                <TableRow key={report.id}>
                  <TableCell>{new Date(report.analysis_date).toLocaleDateString()}</TableCell>
                  <TableCell>{new Date(report.created_at).toLocaleString()}</TableCell>
                  <TableCell>{new Date(report.updated_at).toLocaleString()}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Feature Gaps Section */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Feature Gaps</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Opportunity Score</TableHead>
                <TableHead>Requesting Users</TableHead>
                <TableHead>Monthly Mentions</TableHead>
                {/* <TableHead>Sentiment Intensity</TableHead> */}
                <TableHead>Feature Coverage</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {featureGaps.map((gap) => (
                <TableRow key={gap.id}>
                  <TableCell>{gap.opportunity_score}</TableCell>
                  <TableCell>{gap.requesting_users}</TableCell>
                  <TableCell>{gap.monthly_mentions}</TableCell>
                  {/* <TableCell>{gap.sentiment_intensity}</TableCell> */}
                  <TableCell>{gap.feature_coverage}%</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Supporting Evidence Section */}
      <Card>
        <CardHeader>
          <CardTitle>Supporting Evidence</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Evidence</TableHead>
                <TableHead>Details</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {evidence.map((item) => (
                <TableRow key={item.id}>
                  <TableCell className="max-w-prose whitespace-normal">
                    {formatReviewComment(item.original_review_comment)}
                  </TableCell>
                  <TableCell>
                    <Link href={`/home/<USER>/${encodeURIComponent(category)}/evidence/${item.id}`}>
                      <Button variant="outline" size="sm">View Details</Button>
                    </Link>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
} 