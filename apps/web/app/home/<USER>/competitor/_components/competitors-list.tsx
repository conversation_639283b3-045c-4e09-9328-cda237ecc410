'use client';
import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Trash2, BarChart2 } from 'lucide-react';
//import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
// import { useToast } from '@kit/ui/use-toast';
import Link from 'next/link';
interface Competitor {
  id: string;
  app_id: string;
  app_name: string;
  app_url: string;
}

interface CompetitorsListProps {
  refreshKey: number;
}

export function CompetitorsList({ refreshKey }: CompetitorsListProps) {
  const [competitors, setCompetitors] = useState<Competitor[]>([]);
  const [loading, setLoading] = useState(true);
//   const supabase = createClientComponentClient();
//   const { toast } = useToast();

  const fetchCompetitors = async () => {
    try {
      const response = await fetch('/api/competitors');
      if (!response.ok) throw new Error('Failed to fetch competitors');
      const data = await response.json();
      setCompetitors(data);
    } catch (error) {
    //   toast({
    //     title: 'Error',
    //     description: 'Failed to fetch competitors',
    //     variant: 'destructive'
    //   });
    } finally {
      setLoading(false);
    }
  };

  

  const handleDelete = async (id: string) => {
    try {
      const response = await fetch('/api/competitors', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id })
      });

      if (!response.ok) throw new Error('Failed to delete competitor');

    //   toast({
    //     title: 'Success',
    //     description: 'Competitor removed successfully'
    //   });
      fetchCompetitors();
    } catch (error) {
    //   toast({
    //     title: 'Error',
    //     description: 'Failed to delete competitor',
    //     variant: 'destructive'
    //   });
    }
  };

  useEffect(() => {
    void fetchCompetitors();
  }, [refreshKey]);

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold tracking-tight">Competitors</h2>
      {competitors.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center h-60">
            <CardDescription>No competitors added yet</CardDescription>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {competitors.map((competitor) => (
            <Card key={competitor.id}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {competitor.app_name}
                </CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDelete(competitor.id)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </CardHeader>
              <CardContent>
                <div className="text-sm">
                  <a 
                    href={competitor.app_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline"
                  >
                    View App
                  </a>
                </div>
                <Link 
                  href={`/competitors/${competitor.app_id}/analysis`}
                  className="inline-flex items-center space-x-2 text-sm text-blue-600 hover:underline"
                >
                  <BarChart2 className="h-4 w-4" />
                  <span>View Analysis</span>
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
} 
