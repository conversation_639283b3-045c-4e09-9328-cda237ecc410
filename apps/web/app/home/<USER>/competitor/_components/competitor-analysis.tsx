'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON>Trigger,
} from '@kit/ui/tabs';
import { Badge } from "@kit/ui/badge";
// import { Progress } from "@kit/ui/progress";
//mport { LineChart, BarChart } from "@kit/ui/charts";
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Star, 
  MessageSquare, 
  DollarSign,
  Target,
  Globe
} from 'lucide-react';


interface CompetitorAnalysis {
  app_id: string;
  analysis: {
    analysis_timestamp: string;
    competitive_analysis: {
      feature_competitiveness: number;
      market_position: string;
      pricing_competitiveness: number;
      user_satisfaction: number;
    };
    market_analysis: {
      competitive_advantages: {
        integration_advantages: string[];
        price_advantages: string[];
        service_advantages: string[];
        unique_features: string[];
      };
      target_audience: {
        business_size: string;
        geographical_focus: {
          primary_regions: string[];
          review_geography: Array<{
            country: string;
            counts: number;
            percentage: number;
          }>;
        };
        industry_focus: string[];
      };
      unique_value_props: {
        differentiators: string[];
        primary_benefits: string[];
        value_score: number;
      };
    };
    performance_metrics: {
      pricing_metrics: {
        avg_price: number;
        price_range: number;
        pricing_competitiveness: number;
      };
      user_metrics: {
        rating: number;
        review_count: number;
        user_satisfaction: number;
      };
    };
    temporal_analysis: {
      review_frequency: {
        reviews_per_day: number;
        reviews_per_month: number;
      };
      seasonal_patterns: {
        monthly_distribution: Record<string, number>;
        peak_months: number[];
      };
    };
  };
}

export function CompetitorAnalysis({ competitorId }: { competitorId: string }) {
  const [analysis, setAnalysis] = useState<CompetitorAnalysis | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchAnalysis = async () => {
      try {
        const response = await fetch(`/api/insights/${competitorId}`);
        if (!response.ok) throw new Error('Failed to fetch analysis');
        const data = await response.json();
        setAnalysis(data);
      } catch (error) {
        console.error('Error fetching analysis:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAnalysis();
  }, [competitorId]);

  if (loading) return <div>Loading...</div>;
  if (!analysis) return <div>No analysis available</div>;

  return (
    <div className="space-y-6">
      <Tabs defaultValue="overview" className="w-full">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="market">Market Analysis</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="temporal">Temporal Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Market Position
                </CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {analysis.analysis.competitive_analysis.market_position}
                </div>
                <p className="text-xs text-muted-foreground">
                  Feature Competitiveness: {analysis.analysis.competitive_analysis.feature_competitiveness}%
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  User Satisfaction
                </CardTitle>
                <Star className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {analysis.analysis.performance_metrics.user_metrics.rating.toFixed(1)}
                </div>
                {/* <Progress 
                  value={analysis.analysis.performance_metrics.user_metrics.user_satisfaction * 100} 
                  className="mt-2"
                /> */}
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Pricing Position
                </CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  ${analysis.analysis.performance_metrics.pricing_metrics.avg_price}
                </div>
                <p className="text-xs text-muted-foreground">
                  Competitiveness: {analysis.analysis.performance_metrics.pricing_metrics.pricing_competitiveness}%
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="market" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Competitive Advantages</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {analysis.analysis.market_analysis.competitive_advantages.unique_features.map((feature, i) => (
                <Badge key={i} variant="secondary" className="mr-2">
                  {feature}
                </Badge>
              ))}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Target Audience</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium">Business Size</h4>
                  <p>{analysis.analysis.market_analysis.target_audience.business_size}</p>
                </div>
                <div>
                  <h4 className="font-medium">Industry Focus</h4>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {analysis.analysis.market_analysis.target_audience.industry_focus.map((industry, i) => (
                      <Badge key={i} variant="outline">
                        {industry}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Add Performance and Temporal tabs content similarly */}
      </Tabs>
    </div>
  );
} 

