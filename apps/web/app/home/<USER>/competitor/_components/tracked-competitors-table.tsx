import React, { useEffect, useState } from 'react';
import { Card } from '@kit/ui/card';
import { Star, Eye, TrendingUp, TrendingDown, Bell } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogFooter,
  AlertDialogCancel
} from '@kit/ui/alert-dialog';

interface Competitor {
  id?: string;
  app_id?: string;
  app_name?: string;
  name?: string;
  categories?: string[];
  pricing_plan?: {
    monthly_price: number;
  };
  monthly_price?: number;
  price?: number;
  rating?: number;
  review_count?: number;
  updated_at?: string;
  trend?: string;
  alert?: string;
}

interface TrackedCompetitorsTableProps {
  refreshKey: number;
}

export function TrackedCompetitorsTable({ refreshKey }: TrackedCompetitorsTableProps) {
  const [competitors, setCompetitors] = useState<Competitor[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAlertDialog, setShowAlertDialog] = useState(false);
  const [alertDialogContent, setAlertDialogContent] = useState('');

  useEffect(() => {
    async function fetchCompetitors() {
      try {
        setLoading(true);
        const res = await fetch('/api/competitors');
        if (!res.ok) throw new Error('Failed to fetch competitors');
        const data = await res.json();
        setCompetitors(data);
      } catch (err: any) {
        setError(err.message || 'Unknown error');
      } finally {
        setLoading(false);
      }
    }
    fetchCompetitors();
  }, [refreshKey]);

  if (loading) {
    return <Card className="p-4">Loading competitors...</Card>;
  }
  if (error) {
    return <Card className="p-4 text-red-500">Error: {error}</Card>;
  }
  if (!competitors || competitors.length === 0) {
    return <Card className="p-4">No competitors found.</Card>;
  }

  return (
    <Card className="p-4">
      <div className="font-bold text-lg mb-4">Tracked Competitors</div>
      <div className="overflow-x-auto">
        <table className="min-w-full text-sm border-separate border-spacing-y-2">
          <thead>
            <tr className="text-gray-500 text-xs">
              <th className="text-left px-4 py-2">App Name</th>
              <th className="text-left px-4 py-2">Categories</th>
              <th className="text-left px-4 py-2">Pricing</th>
              <th className="text-left px-4 py-2">Reviews</th>
              <th className="text-center px-4 py-2">Trend</th>
              <th className="text-center px-4 py-2">Alerts</th>
              <th className="text-center px-4 py-2">Actions</th>
            </tr>
          </thead>
          <tbody>
            {competitors.map((comp) => (
              <tr key={comp.id || comp.app_id} className="rounded-lg shadow-sm">
                <td className="px-4 py-2 font-medium">
                  <div>{comp.app_name || comp.name}</div>
                  <div className="text-xs text-gray-400">
                    Updated {comp.updated_at ? new Date(comp.updated_at).toLocaleDateString() : 'N/A'}
                  </div>
                </td>
                <td className="px-4 py-2">
                  {comp.categories && comp.categories.length > 0 ? (
                    <div className="flex flex-wrap gap-1">
                      {comp.categories.map((cat) => (
                        <span
                          key={cat}
                          className="bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full text-xs"
                        >
                          {cat}
                        </span>
                      ))}
                    </div>
                  ) : (
                    <span className="text-gray-400 text-xs">N/A</span>
                  )}
                </td>
                <td className="px-4 py-2">
                  {comp.pricing_plan?.monthly_price
                    ? `$${comp.pricing_plan.monthly_price}/month`
                    : comp.monthly_price
                    ? `$${comp.monthly_price}/month`
                    : comp.price
                    ? `$${comp.price}`
                    : 'N/A'}
                </td>
                <td className="px-4 py-2">
                  <span className="inline-flex items-center gap-1 text-yellow-500 font-semibold">
                    <Star className="w-4 h-4" />
                    {comp.rating ? comp.rating : 'N/A'}
                  </span>
                  {comp.review_count !== undefined && (
                    <span className="ml-2 text-xs text-gray-500">({comp.review_count} reviews)</span>
                  )}
                </td>
                <td className="px-4 py-2 text-center">
                  {comp.trend && comp.trend !== 'N/A' ? (
                    (() => {
                      const parts = comp.trend.split(' ');
                      const status = parts[0];
                      const value = parts.length > 1 ? parts[1] : '';

                      if (status === 'Up') {
                        return (
                          <span className="text-green-600 flex items-center justify-center gap-1">
                            <TrendingUp className="w-3 h-3" />{value}
                          </span>
                        );
                      } else if (status === 'Down') {
                        return (
                          <span className="text-red-600 flex items-center justify-center gap-1">
                            <TrendingDown className="w-3 h-3" />{value}
                          </span>
                        );
                      } else if (status === 'Stable') {
                         return (
                           <span className="text-gray-500 flex items-center justify-center">
                             Stable
                           </span>
                         );
                      } else {
                         return comp.trend;
                      }
                    })()
                  ) : (
                    'N/A'
                  )}
                </td>
                <td className="px-4 py-2 text-center">
                  {comp.alert && comp.alert !== 'N/A' ? (
                    <button
                      onClick={() => {
                        setAlertDialogContent(comp.alert || '');
                        setShowAlertDialog(true);
                      }}
                      className="text-blue-500 hover:opacity-80"
                      aria-label="Show alert details"
                    >
                      <Bell className="w-5 h-5" />
                    </button>
                  ) : (
                    'N/A'
                  )}
                </td>
                <td className="px-4 py-2 text-center">
                  <Eye className="w-5 h-5 text-blue-500 cursor-pointer hover:opacity-80" />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <AlertDialog open={showAlertDialog} onOpenChange={setShowAlertDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Alert Details</AlertDialogTitle>
            <div className="py-4 break-words">{alertDialogContent}</div>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Close</AlertDialogCancel>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
} 