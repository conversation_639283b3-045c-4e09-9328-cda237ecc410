'use client';
import { useState } from 'react';
import { Button } from '@kit/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@kit/ui/dialog';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { Plus } from 'lucide-react';
import { toast } from 'sonner';
import { Checkbox } from '@kit/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@kit/ui/radio-group';
import { Separator } from '@kit/ui/separator';
import appConfig from '~/config/app.config';

interface AddCompetitorDialogProps {
  onCompetitorAdded: () => void;
  competitorCount: number;
}

type TrackingFrequency = 'daily' | 'weekly' | 'monthly';

interface TrackingOptions {
  reviews: boolean;
  features: boolean;
  pricing: boolean;
  frequency: TrackingFrequency;
}

export function AddCompetitorDialog({ onCompetitorAdded, competitorCount }: AddCompetitorDialogProps) {
  const [open, setOpen] = useState(false);
  const [appUrl, setAppUrl] = useState('');
  const [trackingOptions, setTrackingOptions] = useState<TrackingOptions>({
    reviews: true,
    features: true,
    pricing: true,
    frequency: 'weekly'
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (competitorCount >= appConfig.maxCompetitors) {
      toast.error('Maximum competitors reached', {
        description: `You can only track up to ${appConfig.maxCompetitors} competitors.`
      });
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/competitors', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          app_url: appUrl,
          tracking_options: trackingOptions
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to add competitor');
      }

      toast.success('Success', {
        description: 'Competitor added successfully'
      });

      setAppUrl('');
      setTrackingOptions({
        reviews: true,
        features: true,
        pricing: true,
        frequency: 'weekly'
      });
      setOpen(false);
      onCompetitorAdded();
    } catch (error) {
      toast.error('Error', {
        description: error instanceof Error ? error.message : 'Failed to add competitor'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Add Competitor
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Add Competitor</DialogTitle>
          <DialogDescription>
            Configure tracking settings for your competitor
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-6 py-4">
            {/* Step 1: URL Input */}
            <div className="space-y-2">
              <Label htmlFor="app_url">App Store URL</Label>
              <Input
                id="app_url"
                value={appUrl}
                onChange={(e) => setAppUrl(e.target.value)}
                type="url"
                placeholder="https://apps.shopify.com/your-app"
                required
              />
            </div>

            <Separator />

            {/* Step 2: Tracking Options */}
            <div className="space-y-4">
              <Label>What to Track</Label>
              <div className="grid grid-cols-3 gap-4">
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="reviews" 
                    checked={trackingOptions.reviews}
                    onCheckedChange={(checked) => 
                      setTrackingOptions(prev => ({ ...prev, reviews: checked as boolean }))
                    }
                  />
                  <Label htmlFor="reviews">Reviews</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="features" 
                    checked={trackingOptions.features}
                    onCheckedChange={(checked) => 
                      setTrackingOptions(prev => ({ ...prev, features: checked as boolean }))
                    }
                  />
                  <Label htmlFor="features">Features</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="pricing" 
                    checked={trackingOptions.pricing}
                    onCheckedChange={(checked) => 
                      setTrackingOptions(prev => ({ ...prev, pricing: checked as boolean }))
                    }
                  />
                  <Label htmlFor="pricing">Pricing</Label>
                </div>
              </div>
            </div>

            <Separator />

            {/* Step 3: Tracking Frequency */}
            <div className="space-y-4">
              <Label>Tracking Frequency</Label>
              <RadioGroup
                value={trackingOptions.frequency}
                onValueChange={(value) => 
                  setTrackingOptions(prev => ({ ...prev, frequency: value as TrackingFrequency }))
                }
                className="grid grid-cols-3 gap-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="daily" id="daily" />
                  <Label htmlFor="daily">Daily</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="weekly" id="weekly" />
                  <Label htmlFor="weekly">Weekly</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="monthly" id="monthly" />
                  <Label htmlFor="monthly">Monthly</Label>
                </div>
              </RadioGroup>
            </div>
          </div>
          <DialogFooter>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Adding...' : 'Add Competitor'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}