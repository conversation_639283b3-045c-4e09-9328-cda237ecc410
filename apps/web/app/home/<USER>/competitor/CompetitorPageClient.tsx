'use client';

import { useState, useEffect } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@kit/ui/tabs';
import { CompetitorsList } from '~/home/<USER>/competitor/_components/competitors-list';
import { CompetitorsGrid } from '~/home/<USER>/competitor/_components/competitors-grid';
import { AddCompetitorDialog } from '~/home/<USER>/competitor/_components/add-competitor-dialog';
import { TrackedCompetitorsTable } from '~/home/<USER>/competitor/_components/tracked-competitors-table';
import { DataFetchErrorBoundary } from '~/components/error-boundary';

export default function CompetitorPageClient() {
  const [tab, setTab] = useState('list');
  const [competitorCount, setCompetitorCount] = useState(0);
  const [refreshKey, setRefreshKey] = useState(0);

  const fetchCompetitorCount = async () => {
    try {
      const response = await fetch('/api/competitors');
      if (!response.ok) {
        throw new Error('Failed to fetch competitor count');
      }
      const data = await response.json();
      setCompetitorCount(data.length);
    } catch (error) {
      console.error('Error fetching competitor count:', error);
    }
  };

  useEffect(() => {
    fetchCompetitorCount();
  }, []);

  return (
    <>
      <div className="flex items-center justify-between mb-6">
        <Tabs value={tab} onValueChange={setTab} className="w-full">
          <TabsList>
            <TabsTrigger value="list">Grid View</TabsTrigger>
            <TabsTrigger value="grid">Table View</TabsTrigger>
          </TabsList>
        </Tabs>
        <AddCompetitorDialog
          onCompetitorAdded={() => {
            fetchCompetitorCount();
            setRefreshKey((prevKey) => prevKey + 1);
          }}
          competitorCount={competitorCount}
        />
      </div>
      <Tabs value={tab} onValueChange={setTab} className="w-full">
        <TabsContent value="list">
          <DataFetchErrorBoundary>
            <CompetitorsList refreshKey={refreshKey} />
          </DataFetchErrorBoundary>
        </TabsContent>
        <TabsContent value="grid">
          <div className="space-y-6">
            <DataFetchErrorBoundary>
              <TrackedCompetitorsTable refreshKey={refreshKey} />
            </DataFetchErrorBoundary>
            {/* <CompetitorsGrid /> */}
          </div>
        </TabsContent>
      </Tabs>
    </>
  );
} 