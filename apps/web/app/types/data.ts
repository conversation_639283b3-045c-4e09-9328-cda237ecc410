export interface Competitor {
  id: string;
  app_id: number;
  user_id: string;
  created_at: string;
  app_name: string;
  app_url: string;
  updated_at: string;
}

export interface App {
  app_id: number;
  app_name: string;
  app_slug: string;
  url: string;
  video_url: string;
  short_description: string;
  long_description: string;
  languages: string[];
  works_with: string[];
  review_count: number;
  rating: number;
  highlights: string[];
  developer: string;
  developer_url: string;
  built_for_shopify: boolean;
  // Additional fields that may be present in different contexts
  name?: string;
  title?: string;
  app_url?: string;
  store_url?: string;
  monthly_price?: number;
  price?: number;
  monthly?: number;
  pricing_plan?: {
    monthly_price?: number;
  };
  categories?: string[];
}

export interface PricingPlan {
  app_id: number;
  plan_id: number;
  plan_name: string;
  billing_frequency: string;
  features: string[];
  monthly_price: number;
  yearly_price: number;
  savings_percentage: number;
  additional_charges: number;
  trial_days_value: number;
  trial_days_type: string;  
  has_trial: boolean;
  is_free: boolean;
}   

export interface Screenshot {
  app_id: number;
  screenshot_id: number;
  image_type: string;
  screenshot_url: string;
  height: number;
  width: number;
}

// Extended types for UI components
export interface AppWithCategories extends App {
  categories: string[];
}

export interface CompetitorWithPricing extends Competitor {
  pricing_plans?: PricingPlan[];
  monthly_price?: number;
  rating?: number;
  review_count?: number;
  categories?: string[];
}

export interface MarketShareData {
  name: string;
  share: string;
  reviewCount: number;
  app: App;
}

export interface TrendData {
  name: string;
  trend: 'up' | 'down' | 'stable';
  change: number;
}

export interface AlertData {
  type: 'rating' | 'reviews' | 'pricing';
  message: string;
  severity: 'low' | 'medium' | 'high';
}

export interface PriceRankData {
  name: string;
  price: number;
  rank: number | 'N/A';
  displayPrice: string;
  app: App;
}

// AG Grid specific types
export interface AGGridParams<T = any> {
  value: T;
  data: any;
  node: any;
  colDef: any;
  column: any;
  api: any;
  columnApi: any;
  context: any;
}

export interface ChartDatum {
  name: string;
  value?: number;
  price?: number;
  rating?: number;
  [key: string]: any;
}