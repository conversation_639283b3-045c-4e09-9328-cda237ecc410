import { NextResponse } from 'next/server';
import { ServerClient } from 'postmark';
import { generateReportPdf } from '../../../lib/generateReportPdf';
import { getCurrentUser, fetchCompetitors, fetchApps, fetchReviewCountsAndRatingsForComparison } from '../../../models/dbHelpers';
import fs from 'fs/promises';
import path from 'path';

// Initialize Postmark client with error checking
if (!process.env.POSTMARK_API_KEY) {
  throw new Error('POSTMARK_API_KEY is not defined in environment variables');
}

const postmarkClient = new ServerClient(process.env.POSTMARK_API_KEY);

export async function POST(request: Request) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { email, name } = body;

    if (!email || !name) {
      return NextResponse.json(
        { success: false, message: 'Email and name are required' },
        { status: 400 }
      );
    }

    // --- Directly fetch and process data for the PDF report --- START
    const competitors = await fetchCompetitors(user.id);

    if (!competitors || competitors.length === 0) {
       // Handle case with no competitors, perhaps return a different PDF or message
       throw new Error('No competitors tracked to generate a report.');
    }

    const appIds = competitors
      .map(comp => Number(comp.app_id))
      .filter(id => !isNaN(id) && id > 0);

    if (appIds.length === 0) {
       throw new Error('No valid app IDs found for tracked competitors.');
    }

    const apps = await fetchApps(appIds);

    if (!apps) {
        console.error('Failed to fetch app details for tracked competitors');
        throw new Error('Failed to fetch app details for report.');
    }

    const mergedCompetitors = competitors.map(comp => {
        const appDetail = apps.find(app => Number(app.app_id) === Number(comp.app_id));
        return {
            ...comp,
            ...(appDetail ? { app_name: appDetail.app_name, categories: appDetail.categories, review_count: appDetail.review_count, rating: appDetail.rating } : { app_name: comp.app_name, categories: [], review_count: 0, rating: 0 })
        };
    });

    const competitorsWithComparisonData = await Promise.all(mergedCompetitors.map(async (comp) => {
        const comparisonData = comp.app_url ? await fetchReviewCountsAndRatingsForComparison(comp.app_url) : null;
        return { ...comp, comparisonData };
    }));

    // Manually replicate the category grouping and market share calculation logic
    const categoriesMap: { [key: string]: any[] } = {}; // Simplified type for demonstration

    competitorsWithComparisonData.forEach(comp => {
        if (comp.categories && comp.categories.length > 0) {
            comp.categories.forEach(category => {
                if (category && typeof category === 'string') {
                    if (!categoriesMap[category]) {
                        categoriesMap[category] = [];
                    }
                    categoriesMap[category].push(comp);
                }
            });
        }
    });

    const marketShareData = [];

    for (const category in categoriesMap) {
        const competitorsInCategory = categoriesMap[category];
        if (!competitorsInCategory || competitorsInCategory.length === 0) continue;

        const totalCategoryReviews = competitorsInCategory.reduce((sum, comp) => sum + (comp.review_count || 0), 0);

        let leader = null;
        let maxLeadershipScore = -1;
        let leaderReviewMarketShare = 0;
        let totalCategoryGrowthPercentage = 0;

        competitorsInCategory.forEach(comp => {
            const appReviewCount = comp.review_count || 0;
            const averageRating = comp.rating || 0;
            const totalReviews = appReviewCount;

            const reviewMarketSharePercentage = totalCategoryReviews > 0 ? (appReviewCount / totalCategoryReviews) * 100 : 0;
            const reviewMarketShareNormalized = reviewMarketSharePercentage / 100;
            const qualityScore = averageRating > 0 ? (averageRating - 1) / 4 : 0;
            const reviewDominance = Math.sqrt(reviewMarketShareNormalized);
            const ratingConfidence = Math.min(totalReviews / 100, 1);
            const leadershipScore = (reviewDominance * 0.5) + (qualityScore * 0.3) + (ratingConfidence * 0.2);

            if (leadershipScore > maxLeadershipScore) {
                maxLeadershipScore = leadershipScore;
                leader = comp;
                leaderReviewMarketShare = reviewMarketSharePercentage;
            }

            let competitorGrowth = 0;
            if (comp.comparisonData) {
                 const currentReviewCount = comp.comparisonData.currentReviewCount;
                 const competitorReviewCount = comp.comparisonData.competitorReviewCount;

                if (currentReviewCount !== null && competitorReviewCount !== null) {
                    if (competitorReviewCount !== 0) {
                         competitorGrowth = ((currentReviewCount - competitorReviewCount) / competitorReviewCount) * 100;
                    } else if (currentReviewCount !== 0) {
                         competitorGrowth = Infinity;
                     } else {
                         competitorGrowth = 0;
                     }
                 } else if (currentReviewCount !== null && competitorReviewCount === null) {
                      competitorGrowth = 0;
                 } else if (currentReviewCount === null && competitorReviewCount !== null) {
                      competitorGrowth = 0;
                 }

            }
             totalCategoryGrowthPercentage += competitorGrowth;
        });

        if (leader) {
             let leaderClassification = 'N/A';
             if (maxLeadershipScore > 0.7 && leaderReviewMarketShare > 20) {
                leaderClassification = "Dominant Leader";
            } else if (maxLeadershipScore > 0.6 && leaderReviewMarketShare > 15) {
                leaderClassification = "Strong Leader";
            } else if (maxLeadershipScore > 0.5 && ((leader as any).rating || 0) > 4) {
                 leaderClassification = "Quality Leader";
            } else if (leaderReviewMarketShare > 10) {
                leaderClassification = "Market Player";
            } else {
                leaderClassification = "Follower";
            }

            const numberOfCompetitorsWithGrowthData = competitorsInCategory.filter(comp => {
                const compData = comp.comparisonData;
                return compData && compData.currentReviewCount !== null && compData.competitorReviewCount !== null;
            }).length;

            let averageGrowth = 'N/A';
            if (numberOfCompetitorsWithGrowthData > 0) {
                const calculatedAverageGrowth = totalCategoryGrowthPercentage / numberOfCompetitorsWithGrowthData;
                if (isFinite(calculatedAverageGrowth)) {
                    averageGrowth = calculatedAverageGrowth.toFixed(1) + '%';
                } else if (calculatedAverageGrowth === Infinity) {
                    averageGrowth = '+Infinity%';
                } else if (calculatedAverageGrowth === -Infinity) {
                    averageGrowth = '-Infinity%';
                }
             } else if (competitorsInCategory.length > 0) {
                 averageGrowth = '0%';
             }

            marketShareData.push({
                name: category,
                leader: (leader as any).app_name || 'N/A',
                marketShare: `${leaderReviewMarketShare.toFixed(1)}%`,
                leaderClassification: leaderClassification,
                averageGrowth: averageGrowth,
            });
        }
    }
    // --- Directly fetch and process data for the PDF report --- END

    // Generate HTML for the report
    let htmlContent = `
      <html>
      <head>
        <title>Weekly Market Share Report</title>
        <style>
          body { font-family: sans-serif; margin: 20px; }
          h1 { color: #333; }
          table { width: 100%; border-collapse: collapse; margin-top: 20px; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f2f2f2; }
        </style>
      </head>
      <body>
        <h1>Weekly Market Share Report for ${name}</h1>
        <p>Here is your weekly report summarizing market share by category:</p>
        <table>
          <thead>
            <tr>
              <th>Category</th>
              <th>Leader</th>
              <th>Market Share</th>
              <th>Average Growth</th>
            </tr>
          </thead>
          <tbody>
    `;

    // Add table rows from data
    marketShareData.forEach((item: any) => {
      htmlContent += `
        <tr>
          <td>${item.name || 'N/A'}</td>
          <td>${item.leader || 'N/A'}</td>
          <td>${item.marketShare || 'N/A'}</td>
          <td>${item.averageGrowth || 'N/A'}</td>
        </tr>
      `;
    });

    htmlContent += `
          </tbody>
        </table>
        <p style="margin-top: 20px;">Report generated on: ${new Date().toLocaleDateString()}</p>
      </body>
      </html>
    `;

    // Generate PDF using Puppeteer
    const pdfBuffer = await generateReportPdf(htmlContent);

    // Define the directory to save reports
    const reportsDir = path.join(process.cwd(), 'generated_reports');

    // Create the directory if it doesn't exist
    await fs.mkdir(reportsDir, { recursive: true });

    // Define the file path for the PDF
    const timestamp = new Date().toISOString().replace(/[:.-]/g, '');
    const filename = `weekly_report_${timestamp}.pdf`;
    const filePath = path.join(reportsDir, filename);

    // Save the PDF buffer locally
    await fs.writeFile(filePath, pdfBuffer);

    // Send email using Postmark with attachment
    const response = await postmarkClient.sendEmail({
      "From": "<EMAIL>", // Ensure this is your verified Postmark sender email
      "To": email,
      "Subject": "Weekly Report from AppStore Insights",
      "HtmlBody": `
        <h1>Weekly Report Available</h1>
        <p>Hello ${name},</p>
        <p>Your weekly market share report is attached as a PDF.</p>
        <p>Thank you for using AppStore Insights.</p>
      `,
      "TextBody": `Hello ${name},
Your weekly market share report is attached as a PDF.
Thank you for using AppStore Insights.`,
      "MessageStream": "outbound", // Or another message stream if you have configured one in Postmark
      "Attachments": [{
        "Name": "weekly_report.pdf", // Filename for the attachment
        "Content": pdfBuffer.toString('base64'), // PDF content as Base64
        "ContentType": "application/pdf", // MIME type for PDF
        "ContentID": "" // ContentID is required by Postmark's Attachment type
      }]
    });

    console.log('Email with PDF sent successfully:', response);

    return NextResponse.json({ success: true, message: 'Email with PDF sent successfully', response: response });

  } catch (error) {
    console.error('Error sending email with PDF:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to send email with PDF', error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
} 