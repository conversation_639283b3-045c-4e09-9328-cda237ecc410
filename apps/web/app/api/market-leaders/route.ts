import { NextResponse } from 'next/server';
import { getCurrentUser, fetchMarketLeadersByCategoryFeature } from '../../../models/dbHelpers';

export async function GET(request: Request) {
  try {
    console.log('Market Leaders API: Starting request');
    const user = await getCurrentUser();
    if (!user) {
      console.log('Market Leaders API: Unauthorized - No user found');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    console.log('Market Leaders API: User authenticated:', user.id);

    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');

    // Fetch market leaders for the specified category, or default to SEO
    console.log(`Market Leaders API: Fetching market leaders for category: ${category || 'SEO (default)'}`);
    const marketLeaders = await fetchMarketLeadersByCategoryFeature(category || undefined);
    
    if (!marketLeaders) {
      console.log('Market Leaders API: No market leaders found');
      return NextResponse.json([]);
    }

    console.log('Market Leaders API: Successfully fetched market leaders:', {
      count: marketLeaders.length,
      leaders: marketLeaders.map(l => ({
        name: l.app_name,
        marketShare: l.marketShare,
        reviewCount: l.review_count
      }))
    });

    return NextResponse.json(marketLeaders);
  } catch (error) {
    console.error('Market Leaders API: Error fetching market leaders:', error);
    return NextResponse.json(
      { error: 'Failed to fetch market leaders' },
      { status: 500 }
    );
  }
} 