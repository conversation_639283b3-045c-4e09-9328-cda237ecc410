import { enhance<PERSON>oute<PERSON>and<PERSON> } from '@kit/next/routes';
import { getLogger } from '@kit/shared/logger';
import { fetchApps, fetchPricingPlans, fetchScreenshots } from '../../../models/dbHelpers';
import { App, PricingPlan, Screenshot, Competitor } from '../../types/data';

export const GET = enhanceRouteHandler(
  async () => {
    const logger = await getLogger();
    const ctx = { name: 'insights.getData' };

    try {
      logger.info(ctx, 'Fetching insights data from Supabase');


      // Step 1: Fetch Shopify app details 
      const shopifyApps = await fetchApps();
      if (!shopifyApps || shopifyApps.length === 0) {
        logger.info(ctx, 'No shopify apps found in Supabase');
        return Response.json([]);
      }

      logger.info({ ...ctx, sample: shopifyApps[0] }, 'Fetched first Shopify app');

      // Step 2: Get distinct app IDs from Shopify apps
      const appIds = Array.from(new Set(shopifyApps.map(c => c.app_id)));

     

      // Step 3: Fetch pricing plans data for the app_ids
      const pricingPlans = await fetchPricingPlans(appIds);
      if (!pricingPlans) {
        logger.error(ctx, 'Failed to fetch pricing plans data');
        return Response.json({ error: 'Failed to fetch pricing plans data' }, { status: 500 });
      }

      // Step 4: Fetch screenshots data for the app_ids
      const screenshots = await fetchScreenshots();
      if (!screenshots) {
        logger.error(ctx, 'Failed to fetch screenshots data');
        return Response.json({ error: 'Failed to fetch screenshots data' }, { status: 500 });
      }

      // Step 5: Merge all data into a single structure
      const mergedData = shopifyApps.map(app => {
        const appPricingPlans = pricingPlans.filter(plan => plan.app_id === app.app_id);
        const formattedPricingPlans = appPricingPlans.map(plan => ({
          plan_id: plan.plan_id,
          plan_name: plan.plan_name,
          billing_frequency: plan.billing_frequency,
          developer: app.developer,
          developer_url: app.developer_url,
          built_for_shopify: app.built_for_shopify,
          pricing_component: {
            monthly_price: plan.monthly_price,
            yearly_price: plan.yearly_price,
            savings_percentage: plan.savings_percentage
          },
          features: plan.features,
          value: plan.trial_days_value,
          type: plan.trial_days_type,
          has_trial: plan.has_trial,
          is_free: plan.is_free
        }));

        const appScreenshots = screenshots.filter(ss => ss.app_id === app.app_id);

        return {
          app_id: app.app_id,
          app_name: app.app_name,
          app_slug: app.app_slug,
          url: app.url,
          video_url: app.video_url,
          short_description: app.short_description,
          long_description: app.long_description,
          highlights: app.highlights,
          languages: app.languages,
          review_count: app.review_count,
          rating: app.rating,
          works_with: app.works_with,
          developer: app.developer,
          developer_url: app.developer_url,
          built_for_shopify: app.built_for_shopify,
          pricing_plans: formattedPricingPlans,
          screenshots: appScreenshots,
          categories: app.categories || []
        };
      });

      const backInStockData = mergedData.find(app => app.app_slug === 'back-in-stock');
      if (backInStockData) {
        logger.info({ ...ctx, app_slug: 'back-in-stock', data: backInStockData }, 'Back-in-stock app data');
      } else {
        logger.warn({ ...ctx, app_slug: 'back-in-stock' }, 'No data found for app_slug: back-in-stock');
      }

      logger.info(ctx, `Successfully fetched ${mergedData.length} records`);
      return Response.json(mergedData, { status: 200 });

    } catch (error) {
      logger.error({ ...ctx, error }, 'Failed to fetch insights data');
      return Response.json({ error: 'Failed to fetch data' }, { status: 500 });
    }
  },
  {
    auth: true,
  }
);
