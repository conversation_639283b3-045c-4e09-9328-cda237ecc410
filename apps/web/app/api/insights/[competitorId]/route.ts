import { NextRequest, NextResponse } from 'next/server';

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ competitorId: string }> }
) {
  const params = await context.params;
  
  // Mock response for development
  const mockData = {
    app_id: params.competitorId,
    app_name: "Sample App",
    analysis: {
      analysis_timestamp: new Date().toISOString(),
      competitive_analysis: {
        feature_competitiveness: 85,
        market_position: "Leader",
        pricing_competitiveness: 75,
        user_satisfaction: 4.5
      },
      market_analysis: {
        competitive_advantages: {
          integration_advantages: ["Easy setup", "Multiple platform support"],
          price_advantages: ["Competitive pricing", "Flexible plans"],
          service_advantages: ["24/7 support", "Quick response"],
          unique_features: ["AI-powered analytics", "Custom reporting"]
        },
        target_audience: {
          business_size: "SMB",
          geographical_focus: {
            primary_regions: ["North America", "Europe"],
            review_geography: [
              { country: "US", counts: 1200, percentage: 60 },
              { country: "UK", counts: 400, percentage: 20 }
            ]
          },
          industry_focus: ["E-commerce", "Retail", "Digital Products"]
        }
      },
      performance_metrics: {
        pricing_metrics: {
          avg_price: 29.99,
          price_range: 50,
          pricing_competitiveness: 80
        },
        user_metrics: {
          rating: 4.5,
          review_count: 1500,
          user_satisfaction: 0.85
        }
      }
    }
  };

  return NextResponse.json(mockData);
} 