import { NextResponse } from 'next/server';
import { getCurrentUser, fetchCompetitors, fetchApps, fetchReviewCountsAndRatingsForComparison } from '../../../models/dbHelpers';
import { Competitor, App } from '../../types/data'; // Import Competitor and App types

// Define the structure for the market share data per category
interface CategoryMarketShareData {
  name: string; // The name of the category (e.g., "Reviews", "Email Marketing")
  leader: string; // The leading competitor in this category among the tracked ones (e.g., "Judge.me", "Klaviyo")
  marketShare: string; // The market share percentage (e.g., "35%", "42%")
  leaderClassification: string; // Add field for leader classification
  change: { // Illustrative change data as actual growth calculation isn't implemented
    value: string; 
    isPositive: boolean;
  };
  progress: number;
  averageGrowth: string; // Add field for average category growth
}

// Define a type for the merged data (Competitor + App details relevant for categories)
interface MergedCompetitorAppData extends Competitor {
  categories?: string[]; // Add categories array which comes from app details
  // app_name is already on Competitor, but ensure consistency after merge
  app_name: string; // Ensure app_name is treated as string after merge, based on Competitor type
  // Include relevant app details for calculations
  review_count?: number;
  rating?: number; // Assuming this is average rating
}

export async function GET() {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Step 1: Fetch competitors for the current user
    const competitors = await fetchCompetitors(user.id);

    if (!competitors || competitors.length === 0) {
      return NextResponse.json([]); // Return empty array if no competitors are tracked
    }

    // Step 2: Extract app_ids from tracked competitors and filter for valid ones
    const appIds = competitors
      .map(comp => Number(comp.app_id))
      .filter(id => !isNaN(id) && id > 0);

    if (appIds.length === 0) {
       return NextResponse.json([]); // Return empty array if no valid app_ids
    }

    // Step 3: Fetch app details for the tracked apps (including categories, review_count, rating)
    const apps = await fetchApps(appIds);

    // Ensure apps is not null before proceeding
    if (!apps) {
        console.error('Failed to fetch app details for tracked competitors');
        return NextResponse.json([]); // Returning empty data allows the frontend to render without a hard error
    }

    // Step 4: Merge competitor data with app details, including review_count and rating
    const mergedCompetitors: MergedCompetitorAppData[] = competitors.map(comp => {
        const appDetail = apps.find(app => Number(app.app_id) === Number(comp.app_id));

        const merged: MergedCompetitorAppData = {
            ...comp, // Start with competitor data
            // Conditionally add app details, overwriting if necessary
            ...(appDetail ? { app_name: appDetail.app_name, categories: appDetail.categories, review_count: appDetail.review_count, rating: appDetail.rating } : { app_name: comp.app_name, categories: [], review_count: 0, rating: 0 })
        };
        return merged;
    });

    // Step 4.1: Fetch comparison data for all merged competitors
    const competitorsWithComparisonData = await Promise.all(mergedCompetitors.map(async (comp) => {
        const comparisonData = comp.app_url ? await fetchReviewCountsAndRatingsForComparison(comp.app_url) : null;
        return { ...comp, comparisonData };
    }));

    // Step 5: Group merged data by category and calculate market share metrics
    const categoriesMap: { [key: string]: (MergedCompetitorAppData & { comparisonData: any | null })[] } = {}; // Update type to include comparisonData

    competitorsWithComparisonData.forEach(comp => { // Iterate through competitors with comparison data
        if (comp.categories && comp.categories.length > 0) {
            comp.categories.forEach(category => {
                if (category && typeof category === 'string') {
                    if (!categoriesMap[category]) {
                        categoriesMap[category] = [];
                    }
                    categoriesMap[category].push(comp);
                }
            });
        }
    });

    const marketShareData: CategoryMarketShareData[] = [];

    for (const category in categoriesMap) {
        const competitorsInCategory = categoriesMap[category];

        if (!competitorsInCategory || competitorsInCategory.length === 0) {
            continue; // Skip if the category has no competitors (shouldn't happen with current logic, but good practice)
        }

        const totalCategoryReviews = competitorsInCategory.reduce((sum, comp) => sum + (comp.review_count || 0), 0);

        let leader: (MergedCompetitorAppData & { comparisonData: any | null }) | null = null; // Update type
        let maxLeadershipScore = -1;
        let leaderReviewMarketShare = 0; // Keep track of leader's market share for classification
        let totalCategoryGrowthPercentage = 0; // Initialize total growth for the category

        competitorsInCategory.forEach(comp => {
            const appReviewCount = comp.review_count || 0;
            const averageRating = comp.rating || 0; // Use available average rating
            const totalReviews = appReviewCount; // Total reviews for the app is appReviewCount

            // Calculate Review Market Share (normalized to 0-100)
            const reviewMarketSharePercentage = totalCategoryReviews > 0 ? (appReviewCount / totalCategoryReviews) * 100 : 0;
            const reviewMarketShareNormalized = reviewMarketSharePercentage / 100; // Normalized to 0-1

            // Calculate Quality Score (0-1 scale)
            const qualityScore = averageRating > 0 ? (averageRating - 1) / 4 : 0; // Uses provided formula

            // Calculate Review Dominance
            const reviewDominance = Math.sqrt(reviewMarketShareNormalized); // Uses provided formula

            // Calculate Rating Confidence
            const ratingConfidence = Math.min(totalReviews / 100, 1); // Uses provided formula

            // Calculate Advanced Leadership Formula
            const leadershipScore = (reviewDominance * 0.5) + (qualityScore * 0.3) + (ratingConfidence * 0.2); // Uses provided formula

            // Determine the leader in this category based on leadership score
            if (leadershipScore > maxLeadershipScore) {
                maxLeadershipScore = leadershipScore;
                leader = comp;
                leaderReviewMarketShare = reviewMarketSharePercentage; // Store leader's market share percentage
            }

            // Calculate individual competitor review count growth percentage for category growth calculation
            let competitorGrowth = 0; // Default to 0 growth
            if (comp.comparisonData) {
                 const currentReviewCount = comp.comparisonData.currentReviewCount;
                 const competitorReviewCount = comp.comparisonData.competitorReviewCount;

                if (currentReviewCount !== null && competitorReviewCount !== null) {
                    if (competitorReviewCount !== 0) {
                         competitorGrowth = ((currentReviewCount - competitorReviewCount) / competitorReviewCount) * 100;
                    } else if (currentReviewCount !== 0) {
                         // Competitor had 0 reviews historically and now has > 0
                         competitorGrowth = Infinity; // Represent significant growth
                     } else {
                         // Both are 0, no change
                         competitorGrowth = 0;
                     }
                 } else if (currentReviewCount !== null && competitorReviewCount === null) {
                      // Historical data missing for competitor, cannot calculate individual growth based on comparison
                     competitorGrowth = 0; // Treat as no growth for averaging
                 } else if (currentReviewCount === null && competitorReviewCount !== null) {
                       // Current data missing for competitor, cannot calculate individual growth based on comparison
                      competitorGrowth = 0; // Treat as no growth for averaging
                 }

            }
             totalCategoryGrowthPercentage += competitorGrowth; // Sum individual growths

        });

        let leaderClassification = 'N/A';

        if (leader) {
             // Determine Leader Classification based on Simple Classification rules
            if (maxLeadershipScore > 0.7 && leaderReviewMarketShare > 20) {
                leaderClassification = "Dominant Leader";
            } else if (maxLeadershipScore > 0.6 && leaderReviewMarketShare > 15) {
                leaderClassification = "Strong Leader";
            } else if (maxLeadershipScore > 0.5 && ((leader as MergedCompetitorAppData).rating || 0) > 4) { // Quality Score > 0.8 is roughly Rating > 4.2 - using 4 for simplicity with integer ratings
                 leaderClassification = "Quality Leader";
            } else if (leaderReviewMarketShare > 10) {
                leaderClassification = "Market Player";
            } else {
                leaderClassification = "Follower";
            }

            const typedLeader = leader as MergedCompetitorAppData;

            const leaderReviewCount = typedLeader.review_count || 0;
            // Use the pre-calculated leaderReviewMarketSharePercentage
            const illustrativeChangeValue = `${Math.random() > 0.5 ? '+' : '-'}${Math.floor(Math.random() * 10) + 1}%`;
            const illustrativeIsPositive = illustrativeChangeValue.startsWith('+');

            // Calculate average growth for the category, handling Infinity and NaN
            const numberOfCompetitorsWithGrowthData = competitorsInCategory.filter(comp => {
                const compData = comp.comparisonData;
                return compData && compData.currentReviewCount !== null && compData.competitorReviewCount !== null;
            }).length;

            let averageGrowth = 'N/A';
            if (numberOfCompetitorsWithGrowthData > 0) {
                const calculatedAverageGrowth = totalCategoryGrowthPercentage / numberOfCompetitorsWithGrowthData;
                if (isFinite(calculatedAverageGrowth)) {
                    averageGrowth = calculatedAverageGrowth.toFixed(1) + '%';
                } else if (calculatedAverageGrowth === Infinity) {
                    averageGrowth = '+Infinity%'; // Indicate significant positive growth
                } else if (calculatedAverageGrowth === -Infinity) {
                    averageGrowth = '-Infinity%'; // Indicate significant negative growth (shouldn't happen with current logic but for completeness)
                }
             } else if (competitorsInCategory.length > 0) {
                 // If there are competitors but no comparable growth data, show 0% or similar
                 averageGrowth = '0%'; // Or 'N/A', depending on desired display for lack of comparison data
             }

            marketShareData.push({
                name: category,
                leader: typedLeader.app_name || 'N/A', // Use leader's app_name
                marketShare: `${leaderReviewMarketShare.toFixed(1)}%`, // Format market share
                leaderClassification: leaderClassification, // Include classification
                change: { value: illustrativeChangeValue, isPositive: illustrativeIsPositive },
                progress: Math.floor(Math.random() * 100), // Add a random progress value back
                averageGrowth: averageGrowth, // Include average category growth
            });
        }
         // TODO: Consider how to handle categories with no tracked competitors or no reviews.
    }

    return NextResponse.json(marketShareData);

  } catch (err) {
    const error = err as Error;
    console.error('Error fetching category market share data:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
} 