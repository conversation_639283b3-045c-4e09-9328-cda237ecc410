export const runtime = 'edge';
export const schedule = '0 8 * * 1'; // Every Monday at 8am UTC

import { NextResponse } from 'next/server';
import { generateMarketShareReportPdf } from '../../../lib/pdfReportGenerator';
import { ServerClient } from 'postmark';

// Initialize Postmark client with error checking
if (!process.env.POSTMARK_API_KEY) {
  throw new Error('POSTMARK_API_KEY is not defined in environment variables');
}
const postmarkClient = new ServerClient(process.env.POSTMARK_API_KEY);

export async function GET() {
  try {
    // TODO: Fetch actual data for the report
    const dummyData = [
      { name: 'Category A', leader: 'Leader A', marketShare: '20%', averageGrowth: '5%' },
      { name: 'Category B', leader: 'Leader B', marketShare: '30%', averageGrowth: '-2%' },
    ]; // Replace with fetched data

    const pdfBuffer = await generateMarketShareReportPdf(dummyData);

    // TODO: Get recipient email(s) - e.g., from your user data
    const recipientEmail = '<EMAIL>'; // Replace with actual recipient
    const recipientName = 'User'; // Replace with actual name

    // Send email using Postmark with attachment
    const response = await postmarkClient.sendEmail({
      "From": "<EMAIL>", // Replace with your verified sender email
      "To": recipientEmail,
      "Subject": "Your Weekly Market Share Report",
      "HtmlBody": `
        <h1>Weekly Report Available</h1>
        <p>Hello ${recipientName},</p>
        <p>Your weekly market share report is attached as a PDF.</p>
        <p>Thank you for using AppStore Insights.</p>
      `,
      "TextBody": `Hello ${recipientName},
Your weekly market share report is attached as a PDF.
Thank you for using AppStore Insights.`,
      "MessageStream": "outbound",
      "Attachments": [{
        "Name": "weekly_market_share_report.pdf",
        "Content": pdfBuffer.toString('base64'),
        "ContentType": "application/pdf",
        "ContentID": "" 
      }]
    });

    console.log('Weekly report email sent successfully:', response);

    return NextResponse.json({ success: true, message: 'Weekly report email triggered successfully' });

  } catch (error) {
    console.error('Error sending weekly report email:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to trigger weekly report email',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
} 