import { NextResponse } from 'next/server';
import { fetchCompetitors, addCompetitor, deleteCompetitor, getCurrentUser, fetchApps, fetchPricingPlans, fetchCompetitorsWithPricingPlans, fetchHistoricalCompetitors, fetchReviewCountsAndRatingsForComparison } from '../../../models/dbHelpers';
import { Competitor, App } from '../../types/data'; // Import App type for historical data structure
import { isSuccess, ValidationError, NotFoundError, DatabaseError } from '../../../lib/errors';

export async function GET() {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Step 1: Fetch competitors for the current user
    const competitors = await fetchCompetitors(user.id);
    if (!competitors || competitors.length === 0) {
      return NextResponse.json([]);
    }

    // Fetch historical data for competitors to calculate trend and alerts
    // This approach uses a new function to compare review counts and ratings directly between two tables.
    // The previous historical fetching logic is not used in this specific trend calculation.
    // type HistoricalAppData = Pick<App, 'app_id' | 'review_count' | 'rating'>; // Define type based on App
    // const historicalData: HistoricalAppData[] | null = await fetchHistoricalCompetitors(user.id, competitors.map(c => c.app_id)); // No longer directly used for THIS trend calculation

    // Step 2: Filter valid app_ids
    const validAppIds = competitors
      .map((comp) => Number(comp.app_id))
      .filter((id) => !isNaN(id) && id > 0);

    if (validAppIds.length === 0) {
      return NextResponse.json(competitors);
    }

    // Step 3: Fetch apps and pricing plans in parallel
    const [apps, pricingPlans] = await Promise.all([
      fetchApps(validAppIds),
      fetchPricingPlans(validAppIds)
    ]);

    // Step 4: Fetch all apps with their pricing_plans
    const allAppsWithPlans = await fetchCompetitorsWithPricingPlans();

    // Step 5: Merge the data and calculate trend/alerts
    const mergedData = await Promise.all(competitors.map(async (comp) => { // Use await and make map callback async
      const appDetails: Partial<App> = apps?.find((app) => Number(app.app_id) === Number(comp.app_id)) || {}; // Use Partial<App>
      const pricingDetails = pricingPlans?.find((p) => Number(p.app_id) === Number(comp.app_id)) || {
        has_trial: null,
        is_free: null,
        trial_days_value: 0,
        monthly_price: null,
        yearly_price: null
      };
      // Attach pricing_plans array if available
      const appWithPlans = allAppsWithPlans?.find((app: any) => Number(app.app_id) === Number(comp.app_id));

      // Fetch review counts and ratings for comparison using the new function
      const comparisonData = comp.app_url ? await fetchReviewCountsAndRatingsForComparison(comp.app_url) : null;

      // Calculate alerts based on comparison data
      let alert = 'N/A'; // Default alert
      const alerts: string[] = [];

      if (comparisonData) {
          const currentReviewCount = comparisonData.currentReviewCount;
          const competitorReviewCount = comparisonData.competitorReviewCount;
          const currentRating = comparisonData.currentRating;
          const competitorRating = comparisonData.competitorRating;

          // Alert for review count change
          if (currentReviewCount !== null && competitorReviewCount !== null && currentReviewCount !== competitorReviewCount) {
              const diff = competitorReviewCount - currentReviewCount;
              if (diff > 0) {
                  alerts.push(`Review count hiked by ${diff} (compared to base table)`);
              } else if (diff < 0) {
                  alerts.push(`Review count dropped by ${Math.abs(diff)} (compared to base table)`);
              }
          } else if (currentReviewCount !== null && competitorReviewCount === null) {
              alerts.push('Historical review count not available in competitor table');
          } else if (currentReviewCount === null && competitorReviewCount !== null) {
               alerts.push('Current review count not available in shopify_apps table');
          } else if (currentReviewCount === null && competitorReviewCount === null) {
               alerts.push('Review count data not available in either table');
          } else if (currentReviewCount !== null && competitorReviewCount !== null && currentReviewCount === competitorReviewCount) {
               alerts.push('Review count unchanged (compared to competitor table)');
          }

          // Alert for rating change (with tolerance)
           const ratingChangeTolerance = 0.1; // Adjust as needed
          if (currentRating !== null && competitorRating !== null && Math.abs(currentRating - competitorRating) > ratingChangeTolerance) {
              const diff = competitorRating - currentRating;
               if (diff > 0) {
                  alerts.push(`Rating hiked by ${diff.toFixed(1)} (compared to base table)`);
              } else if (diff < 0) {
                  alerts.push(`Rating dropped by ${Math.abs(diff).toFixed(1)} (compared to base table)`);
              }
          } else if (currentRating !== null && competitorRating === null) {
              alerts.push('Historical rating not available in competitor table');
          } else if (currentRating === null && competitorRating !== null) {
              alerts.push('Current rating not available in shopify_apps table');
          } else if (currentRating === null && competitorRating === null) {
               alerts.push('Rating data not available in either table');
          } else if (currentRating !== null && competitorRating !== null && Math.abs(currentRating - competitorRating) <= ratingChangeTolerance) {
              alerts.push('Rating unchanged (compared to competitor table)');
          }
      }

      // Join all alerts into a single string, or set to 'N/A' if no alerts
      // Remove text within parentheses from alerts
      const cleanedAlerts = alerts.map(alertText => alertText.replace(/\s*\([^)]*\)/g, '').trim());
      alert = cleanedAlerts.length > 0 ? cleanedAlerts.join(', ') : 'N/A';

      // Calculate trend based on comparison data (considering both review count and rating)
      // Trend will include status (Up, Down, Stable) and a value/percentage
      let trend = 'N/A'; // Default trend
      let trendValue: number | string = ''; // Value or percentage for the trend

      if (comparisonData) {
          const currentReviewCount = comparisonData.currentReviewCount;
          const competitorReviewCount = comparisonData.competitorReviewCount;
          const currentRating = comparisonData.currentRating;
          const competitorRating = comparisonData.competitorRating;

          // Prioritize review count trend
          if (currentReviewCount !== null && competitorReviewCount !== null) {
              const reviewCountComparisonDiff = competitorReviewCount - currentReviewCount;
              if (reviewCountComparisonDiff !== 0) {
                  trend = reviewCountComparisonDiff > 0 ? 'Up' : 'Down';
                  // Calculate percentage change if currentReviewCount is not zero
                  if (currentReviewCount !== 0) {
                      trendValue = `${((reviewCountComparisonDiff / currentReviewCount) * 100).toFixed(0)}%`;
                  } else {
                      // If current is zero, just show the absolute difference
                      trendValue = `${reviewCountComparisonDiff > 0 ? '+' : ''}${reviewCountComparisonDiff}`;
                  }
              } else {
                  // If review count is unchanged, check rating trend
                  if (currentRating !== null && competitorRating !== null) {
                       const ratingComparisonDiff = competitorRating - currentRating;
                       const ratingChangeTolerance = 0.1; // Same tolerance as alerts

                      if (Math.abs(ratingComparisonDiff) > ratingChangeTolerance) {
                          trend = ratingComparisonDiff > 0 ? 'Up' : 'Down';
                          trendValue = `${ratingComparisonDiff > 0 ? '+' : ''}${ratingComparisonDiff.toFixed(1)}`;
                      } else {
                           trend = 'Stable';
                           trendValue = ''; // No value for stable
                      }
                  } else {
                      trend = 'Stable'; // Stable if review count unchanged but rating data is incomplete
                      trendValue = '';
                  }
              }
          }
           // No trend calculation if comparison data is not available
      }

      // Format the trend string to be consumed by the frontend
      const formattedTrend = trendValue !== '' ? `${trend} ${trendValue}` : trend;

      return {
        ...comp,
        ...appDetails,
        has_trial: pricingDetails.has_trial ?? null,
        is_free: pricingDetails.is_free ?? null,
        trial_days: pricingDetails.trial_days_value ?? 0,
        monthly_price: pricingDetails.monthly_price ?? null,
        yearly_price: pricingDetails.yearly_price ?? null,
        pricing_plans: appWithPlans?.pricing_plans || [],
        categories: (appDetails as { categories?: string[] }).categories || [],
        trend: formattedTrend, // Include formatted trend in the response
        alert // Include alert in the response
      };
    })); // Close map and await Promise.all

    return NextResponse.json(mergedData);
  } catch (err) {
    const error = err as Error;
    console.error('Unexpected error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { app_url } = await request.json();

    const result = await addCompetitor(app_url, user.id);

    if (!isSuccess(result)) {
      const { error } = result;

      if (error instanceof ValidationError) {
        return NextResponse.json({
          error: error.message,
          field: error.field
        }, { status: 400 });
      }

      if (error instanceof NotFoundError) {
        return NextResponse.json({
          error: error.message,
          resource: error.resource
        }, { status: 404 });
      }

      if (error instanceof DatabaseError) {
        return NextResponse.json({
          error: 'Database operation failed',
          details: error.message
        }, { status: 500 });
      }

      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: result.data
    });
  } catch (err) {
    const error = err as Error;
    return NextResponse.json({
      error: 'Internal server error',
      details: error.message
    }, { status: 500 });
  }
}

export async function DELETE(request: Request) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await request.json();

    const result = await deleteCompetitor(id, user.id);

    if (!isSuccess(result)) {
      const { error } = result;

      if (error instanceof ValidationError) {
        return NextResponse.json({
          error: error.message,
          field: error.field
        }, { status: 400 });
      }

      if (error instanceof NotFoundError) {
        return NextResponse.json({
          error: error.message,
          resource: error.resource
        }, { status: 404 });
      }

      if (error instanceof DatabaseError) {
        return NextResponse.json({
          error: 'Database operation failed',
          details: error.message
        }, { status: 500 });
      }

      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (err) {
    const error = err as Error;
    return NextResponse.json({
      error: 'Internal server error',
      details: error.message
    }, { status: 500 });
  }
}