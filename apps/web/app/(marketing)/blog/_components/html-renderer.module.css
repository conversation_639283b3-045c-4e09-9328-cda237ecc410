.HTML {
  @apply text-secondary-foreground;
}

.HTML h1 {
  @apply mt-14 text-4xl font-semibold font-heading tracking-tight;
}

.HTML h2 {
  @apply mb-6 mt-12 font-semibold text-2xl font-heading tracking-tight;
}

.HTML h3 {
  @apply mt-12 text-xl font-semibold font-heading tracking-tight;
}

.HTML h4 {
  @apply mt-8 text-lg font-medium tracking-tight;
}

.HTML h5 {
  @apply mt-6 text-base font-medium tracking-tight;
}

.HTML h6 {
  @apply mt-2 text-sm font-normal tracking-tight;
}

/**
Tailwind "dark" variants do not work with CSS Modules
We work it around using :global(.dark)
For more info: https://github.com/tailwindlabs/tailwindcss/issues/3258#issuecomment-770215347
*/
:global(.dark) .HTML h1,
:global(.dark) .HTML h2,
:global(.dark) .HTML h3,
:global(.dark) .HTML h4,
:global(.dark) .HTML h5,
:global(.dark) .HTML h6 {
  @apply text-white;
}

.HTML p {
  @apply mb-6 mt-4 text-base leading-7 text-muted-foreground;
}

.HTML li {
  @apply relative my-1.5 text-base leading-7 text-muted-foreground;
}

.HTML ul > li:before {
  content: '-';

  @apply mr-2;
}

.HTML ol > li:before {
  @apply inline-flex font-medium text-muted-foreground;

  content: counters(counts, '.') '. ';
  font-feature-settings: 'tnum';
}

.HTML b,
.HTML strong {
  @apply font-semibold text-secondary-foreground;
}

:global(.dark) .HTML b,
:global(.dark) .HTML strong {
  @apply text-white;
}

.HTML img,
.HTML video {
  @apply rounded-md;
}

.HTML ul,
.HTML ol {
  @apply pl-1;
}

.HTML ol > li {
  counter-increment: counts;
}

.HTML ol > li:before {
  @apply mr-2 inline-flex font-semibold;

  content: counters(counts, '.') '. ';
  font-feature-settings: 'tnum';
}

.HTML p > code, .HTML li > code {
  @apply p-0.5 text-sm font-semibold bg-muted/50 border font-mono text-secondary-foreground;
}

.HTML blockquote {
  @apply my-4 border-l-8 border border-primary px-6 py-4 text-lg font-medium text-muted-foreground;
}

.HTML a {
  @apply border-b-black border-b hover:border-b-2 pb-0.5 text-secondary-foreground font-semibold;
}

:global(.dark) .HTML a {
  @apply border-yellow-300;
}

.HTML hr {
  @apply mt-8 mb-6 border-border;
}

.HTML [role='alert'] {
  @apply py-4 m-0 my-8;
}

.HTML [role='alert'] * {
  color: inherit;
  @apply m-0 p-0 text-sm;
}

.HTML [role='alert'] h5 {
  color: inherit;
}