import { CompetitorAnalysis } from '~/home/<USER>/competitor/_components/competitor-analysis';
import { PageBody } from '@kit/ui/page';

interface PageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function CompetitorAnalysisPage({ params }: PageProps) {
  const { id } = await params;

  return (
    <PageBody>
      <div className="flex w-full flex-1 flex-col">
        <CompetitorAnalysis competitorId={id} />
      </div>
    </PageBody>
  );
} 
