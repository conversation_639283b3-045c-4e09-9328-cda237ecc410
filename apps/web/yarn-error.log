Arguments: 
  /opt/homebrew/Cellar/node/23.11.0/bin/node /usr/local/Cellar/yarn/1.22.17/libexec/bin/yarn.js add @tanstack/react-query-devtools

PATH: 
  /Users/<USER>/Library/Application Support/cloud-code/installer/google-cloud-sdk/bin:/Users/<USER>/.local/bin:/Users/<USER>/Library/Android/sdk/platform-tools:/Users/<USER>/.rvm/gems/ruby-2.7.5/bin:/Users/<USER>/.rvm/gems/ruby-2.7.5@global/bin:/Users/<USER>/.rvm/rubies/ruby-2.7.5/bin:/Users/<USER>/.rvm/bin:/opt/homebrew/opt/mongodb-community@4.4/bin:/Users/<USER>/google-cloud-sdk/bin:/Users/<USER>/.rbenv/shims:/Users/<USER>/.nvm/versions/node/v16.13.1/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin:/Library/Apple/usr/bin:/Users/<USER>/.cargo/bin:/Users/<USER>/flutter/bin:/usr/local/mysql/bin

Yarn version: 
  1.22.17

Node version: 
  23.11.0

Platform: 
  darwin arm64

Trace: 
  Error: https://registry.yarnpkg.com/@kit%2faccounts: Not found
      at params.callback [as _callback] (/usr/local/Cellar/yarn/1.22.17/libexec/lib/cli.js:67029:18)
      at self.callback (/usr/local/Cellar/yarn/1.22.17/libexec/lib/cli.js:140883:22)
      at Request.emit (node:events:507:28)
      at Request.<anonymous> (/usr/local/Cellar/yarn/1.22.17/libexec/lib/cli.js:141855:10)
      at Request.emit (node:events:507:28)
      at IncomingMessage.<anonymous> (/usr/local/Cellar/yarn/1.22.17/libexec/lib/cli.js:141777:12)
      at Object.onceWrapper (node:events:621:28)
      at IncomingMessage.emit (node:events:519:35)
      at endReadableNT (node:internal/streams/readable:1696:12)
      at process.processTicksAndRejections (node:internal/process/task_queues:90:21)

npm manifest: 
  {
    "name": "web",
    "version": "0.1.0",
    "private": true,
    "sideEffects": false,
    "type": "module",
    "scripts": {
      "analyze": "ANALYZE=true pnpm run build",
      "build": "pnpm with-env next build",
      "build:test": "NODE_ENV=test pnpm with-env:test next build",
      "clean": "git clean -xdf .next .turbo node_modules",
      "dev": "pnpm with-env next dev --turbo | pino-pretty -c",
      "lint": "next lint && eslint .",
      "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,md,json}\"",
      "start": "pnpm with-env next start",
      "start:test": "NODE_ENV=test pnpm with-env:test next start",
      "typecheck": "tsc --noEmit",
      "with-env": "dotenv -e ./.env.local --",
      "with-env:test": "dotenv -e ./.env.test --",
      "supabase": "supabase",
      "supabase:start": "supabase status || supabase start",
      "supabase:stop": "supabase stop",
      "supabase:reset": "supabase db reset",
      "supabase:status": "supabase status",
      "supabase:test": "supabase db test",
      "supabase:db:lint": "supabase db lint",
      "supabase:deploy": "supabase link --project-ref $SUPABASE_PROJECT_REF && supabase db push",
      "supabase:typegen": "pnpm run supabase:typegen:packages && pnpm run supabase:typegen:app",
      "supabase:typegen:packages": "supabase gen types typescript --local > ../../packages/supabase/src/database.types.ts",
      "supabase:typegen:app": "supabase gen types typescript --local > ./lib/database.types.ts",
      "supabase:db:dump:local": "supabase db dump --local --data-only"
    },
    "dependencies": {
      "@edge-csrf/nextjs": "2.5.3-cloudflare-rc1",
      "@google-cloud/bigquery": "^7.9.1",
      "@hookform/resolvers": "^3.9.1",
      "@kit/accounts": "workspace:*",
      "@kit/admin": "workspace:*",
      "@kit/analytics": "workspace:*",
      "@kit/auth": "workspace:*",
      "@kit/billing": "workspace:*",
      "@kit/billing-gateway": "workspace:*",
      "@kit/cms": "workspace:*",
      "@kit/database-webhooks": "workspace:*",
      "@kit/email-templates": "workspace:*",
      "@kit/i18n": "workspace:*",
      "@kit/mailers": "workspace:*",
      "@kit/monitoring": "workspace:*",
      "@kit/next": "workspace:*",
      "@kit/notifications": "workspace:*",
      "@kit/shared": "workspace:*",
      "@kit/supabase": "workspace:*",
      "@kit/team-accounts": "workspace:*",
      "@kit/ui": "workspace:*",
      "@makerkit/data-loader-supabase-core": "^0.0.8",
      "@makerkit/data-loader-supabase-nextjs": "^1.2.3",
      "@marsidev/react-turnstile": "^1.1.0",
      "@radix-ui/react-icons": "^1.3.2",
      "@radix-ui/react-progress": "^1.1.1",
      "@supabase/supabase-js": "^2.47.3",
      "@tanstack/react-query": "5.62.7",
      "@tanstack/react-table": "^8.20.5",
      "add": "^2.0.6",
      "ag-charts-community": "^11.3.1",
      "ag-charts-react": "^11.3.1",
      "ag-grid-community": "^33.0.3",
      "ag-grid-react": "^33.0.3",
      "clsx": "^2.1.1",
      "date-fns": "^4.1.0",
      "i": "^0.3.7",
      "lucide-react": "^0.468.0",
      "next": "15.1.0",
      "next-sitemap": "^4.2.3",
      "next-themes": "0.4.4",
      "pdf-lib": "^1.17.1",
      "postmark": "^4.0.5",
      "progress": "^2.0.3",
      "puppeteer": "^24.10.0",
      "react": "19.0.0",
      "react-dom": "19.0.0",
      "react-hook-form": "^7.54.0",
      "react-i18next": "^15.1.4",
      "react-icons": "^5.4.0",
      "recharts": "2.14.1",
      "shadcn": "^2.1.8",
      "sonner": "^1.7.1",
      "tailwind-merge": "^2.5.5",
      "zod": "^3.24.1"
    },
    "devDependencies": {
      "@kit/eslint-config": "workspace:*",
      "@kit/prettier-config": "workspace:*",
      "@kit/tailwind-config": "workspace:*",
      "@kit/tsconfig": "workspace:*",
      "@next/bundle-analyzer": "15.1.0",
      "@types/mdx": "^2.0.13",
      "@types/node": "^22.10.1",
      "@types/react": "npm:types-react@19.0.0-rc.1",
      "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1",
      "autoprefixer": "^10.4.20",
      "babel-plugin-react-compiler": "19.0.0-beta-df7b47d-20241124",
      "dotenv-cli": "^7.4.4",
      "eslint": "^8.57.0",
      "import-in-the-middle": "1.11.3",
      "pino-pretty": "^13.0.0",
      "prettier": "^3.4.2",
      "require-in-the-middle": "7.4.0",
      "supabase": "^2.0.0",
      "tailwindcss": "3.4.16",
      "typescript": "^5.7.2"
    },
    "eslintConfig": {
      "root": true,
      "extends": [
        "@kit/eslint-config/base",
        "@kit/eslint-config/nextjs",
        "@kit/eslint-config/react",
        "@kit/eslint-config/apps"
      ]
    },
    "prettier": "@kit/prettier-config",
    "browserslist": [
      "iOS >= 9",
      "Android >= 4.4",
      "last 2 versions",
      "> 0.2%",
      "not dead"
    ]
  }

yarn manifest: 
  No manifest

Lockfile: 
  No lockfile
