import { supabase } from '../lib/supabase/client';
import { getSupabaseRouteHandlerClient } from '@kit/supabase/route-handler-client';
import { getLogger } from '@kit/shared/logger';
import { cookies } from 'next/headers';
import { App, Competitor, PricingPlan, Screenshot } from '../app/types/data';
import { DatabaseError, NotFoundError, ValidationError, Result, success, failure, safeAsync } from '../lib/errors';

export async function fetchCompetitors(user_id?: string): Promise<Competitor[] | null> {
  const logger = await getLogger();
  const ctx = { name: 'fetchCompetitors', user_id };

  const query = supabase
    .from('competitors')
    .select(`
      id,
      app_id,
      user_id,
      created_at,
      app_name,
      app_url,
      updated_at
    `);

  if (user_id) {
    query.eq('user_id', user_id);
  }

  const { data, error } = await query;

  if (error) {
    logger.error({ ...ctx, error: error.message }, 'Failed to fetch competitors from Supabase');
    return null;
  }

  if (!data) {
    logger.warn(ctx, 'No competitors data returned from Supabase');
    return null;
  }

  logger.info({ ...ctx, count: data.length }, 'Successfully fetched competitors');
  return data as unknown as Competitor[];
}

export async function fetchApps(appIds?: number[]): Promise<(App & { categories: string[] })[] | null> {
  const logger = await getLogger();
  const ctx = { name: 'fetchApps', appIds: appIds?.length };

  const query = supabase
    .from('shopify_apps')
    .select(`
      app_id,
      app_name,
      app_slug,
      url,
      video_url,
      short_description,
      long_description,
      languages,
      works_with,
      review_count,
      rating,
      highlights,
      developer,
      developer_url,
      built_for_shopify,
      category_features
    `);

  if (appIds && appIds.length > 0) {
    query.in('app_id', appIds);
  }

  const { data, error } = await query;

  if (error) {
    logger.error({ ...ctx, error: error.message }, 'Failed to fetch shopify apps data');
    return null;
  }

  if (!data || data.length === 0) {
    logger.info(ctx, 'No shopify apps found in Supabase');
    return null;
  }

  const appsWithCategories = data.map(({ category_features, ...app }) => {
    let categories: string[] = [];

    if (typeof category_features === 'string') {
      try {
        const parsed = JSON.parse(category_features);
        if (Array.isArray(parsed)) {
          categories = parsed.map(f => f?.heading).filter(Boolean);
        }
      } catch (err) {
        logger.warn({ ...ctx, app_name: app.app_name, error: err }, 'Failed to parse category_features for app');
      }
    } else if (Array.isArray(category_features)) {
      categories = category_features.map(f => f?.heading).filter(Boolean);
    }

    const uniqueCategories = Array.from(new Set(categories));

    return {
      ...app,
      categories: uniqueCategories,
    };
  });

  logger.info({ ...ctx, count: appsWithCategories.length }, 'Successfully fetched and processed shopify apps');
  return appsWithCategories;
}


export async function fetchPricingPlans(appIds?: number[]): Promise<PricingPlan[] | null> {
  const logger = await getLogger();
  const ctx = { name: 'fetchPricingPlans', appIds: appIds?.length };

  const query = supabase
    .from('pricing_plans')
    .select(`
      app_id,
      plan_id,
      plan_name,
      billing_frequency,
      features,
      monthly_price,
      yearly_price,
      savings_percentage,
      additional_charges,
      trial_days_value,
      trial_days_type,
      has_trial,
      is_free
    `);

  if (appIds && appIds.length > 0) {
    query.in('app_id', appIds);
  }

  const { data, error } = await query;

  if (error) {
    logger.error({ ...ctx, error: error.message }, 'Failed to fetch pricing plans data');
    return null;
  }

  if (!data || data.length === 0) {
    logger.info(ctx, 'No pricing plans found in Supabase');
    return null;
  }

  logger.info({ ...ctx, count: data.length }, 'Successfully fetched pricing plans');
  return data as PricingPlan[];
}

export async function fetchScreenshots(appIds?: number[]): Promise<Screenshot[] | null> {
  const logger = await getLogger();
  const ctx = { name: 'fetchScreenshots', appIds: appIds?.length };

  const query = supabase
    .from('screenshots')
    .select(`
      app_id,
      screenshot_id,
      image_type,
      screenshot_url,
      height,
      width
    `);

  if (appIds && appIds.length > 0) {
    query.in('app_id', appIds);
  }

  const { data, error } = await query;

  if (error) {
    logger.error({ ...ctx, error: error.message }, 'Failed to fetch screenshots data');
    return null;
  }

  if (!data || data.length === 0) {
    logger.info(ctx, 'No screenshots found in Supabase');
    return null;
  }

  logger.info({ ...ctx, count: data.length }, 'Successfully fetched screenshots');
  return data as Screenshot[];
}

export async function fetchAppsByCategoryFeature(categoryFeatureHeading: string = 'SEO'): Promise<(App & { categories: string[] })[] | null> {
  const logger = await getLogger();
  const ctx = { name: 'fetchAppsByCategoryFeature', categoryFeatureHeading };

  // Calling a PostgreSQL function (RPC) to handle complex JSON array querying.
  // This method directly uses the working SQL query you provided, encapsulated in a database function.
  const { data, error } = await supabase.rpc('get_apps_by_category_feature', { target_heading: categoryFeatureHeading });

  if (error) {
    logger.error({ ...ctx, error: error.message }, 'Failed to fetch shopify apps by category feature using RPC');
    return null;
  }

  if (!data || data.length === 0) {
    logger.info(ctx, 'No shopify apps found for category feature');
    return null;
  }

  // The RPC returns data directly from the shopify_apps table, so we need to re-extract categories
  // as your App type expects them as a separate 'categories' array.
  const appsWithCategories = data.map((app: any) => {
    let categories: string[] = [];
    // Ensure category_features is treated as string for parsing if it's JSON type
    const categoryFeatures = typeof app.category_features === 'string' ? JSON.parse(app.category_features) : app.category_features;

    if (Array.isArray(categoryFeatures)) {
      categories = categoryFeatures.map((f: any) => f?.heading).filter(Boolean);
    }

    const uniqueCategories = Array.from(new Set(categories));

    return {
      ...app,
      categories: uniqueCategories,
    };
  });
  return appsWithCategories;
}

export async function getAppDetailsByUrl(appUrl: string): Promise<App | null> {
  const logger = await getLogger();
  const ctx = { name: 'getAppDetailsByUrl', appUrl };

  const { data, error } = await supabase
    .from('shopify_apps')
    .select('*')
    .eq('url', appUrl)
    .single();

  if (error) {
    logger.error({ ...ctx, error: error.message }, 'Error fetching app details by URL');
    return null;
  }

  logger.info(ctx, 'Successfully fetched app details by URL');
  return data as App;
}

export async function addCompetitor(appUrl: string, userId: string): Promise<Result<{ app_id: number }, DatabaseError | NotFoundError | ValidationError>> {
  const logger = await getLogger();
  const ctx = { name: 'addCompetitor', appUrl, userId };

  // Validate inputs
  if (!appUrl?.trim()) {
    const error = new ValidationError('App URL is required', 'appUrl', appUrl);
    logger.error({ ...ctx, error: error.message }, 'Validation failed');
    return failure(error);
  }

  if (!userId?.trim()) {
    const error = new ValidationError('User ID is required', 'userId', userId);
    logger.error({ ...ctx, error: error.message }, 'Validation failed');
    return failure(error);
  }

  return safeAsync(async () => {
    // First get the app details from shopify_apps table
    const appDetails = await getAppDetailsByUrl(appUrl);

    if (!appDetails) {
      throw new NotFoundError('App not found in Shopify App Store', 'shopify_app', appUrl);
    }

    const competitorData = {
      app_id: appDetails.app_id,
      user_id: userId,
      app_name: appDetails.app_name,
      app_url: appUrl
    };

    const { error } = await supabase
      .from('competitors')
      .insert([competitorData]);

    if (error) {
      throw new DatabaseError(
        'Failed to insert competitor',
        'insert',
        'competitors',
        error
      );
    }

    logger.info({ ...ctx, app_id: appDetails.app_id }, 'Successfully added competitor');
    return { app_id: appDetails.app_id };
  });
}

export async function deleteCompetitor(id: string, userId: string): Promise<Result<void, DatabaseError | NotFoundError | ValidationError>> {
  const logger = await getLogger();
  const ctx = { name: 'deleteCompetitor', id, userId };

  // Validate inputs
  if (!id?.trim()) {
    const error = new ValidationError('Competitor ID is required', 'id', id);
    logger.error({ ...ctx, error: error.message }, 'Validation failed');
    return failure(error);
  }

  if (!userId?.trim()) {
    const error = new ValidationError('User ID is required', 'userId', userId);
    logger.error({ ...ctx, error: error.message }, 'Validation failed');
    return failure(error);
  }

  return safeAsync(async () => {
    // First verify the competitor exists and belongs to the user
    const { data: existingCompetitors, error: fetchError } = await supabase
      .from('competitors')
      .select('id')
      .eq('id', id)
      .eq('user_id', userId);

    if (fetchError) {
      throw new DatabaseError(
        'Failed to verify competitor ownership',
        'select',
        'competitors',
        fetchError
      );
    }

    if (!existingCompetitors || existingCompetitors.length === 0) {
      throw new NotFoundError('Competitor not found or access denied', 'competitor', id);
    }

    // Perform the deletion
    const { error: deleteError } = await supabase
      .from('competitors')
      .delete()
      .eq('id', id)
      .eq('user_id', userId);

    if (deleteError) {
      throw new DatabaseError(
        'Failed to delete competitor',
        'delete',
        'competitors',
        deleteError
      );
    }

    logger.info(ctx, 'Successfully deleted competitor');
  });
}

export async function getCurrentUser() {
  const logger = await getLogger();
  const ctx = { name: 'getCurrentUser' };

  const supabase = getSupabaseRouteHandlerClient();
  const { data: { user }, error } = await supabase.auth.getUser();
  if (error) {
    logger.error({ ...ctx, error: error.message }, 'Error getting current user');
    return null;
  }

  logger.info({ ...ctx, userId: user?.id }, 'Successfully retrieved current user');
  return user;
}

export async function fetchCompetitorsWithPricingPlans() {
  const { data, error } = await supabase
    .from('shopify_apps')
    .select(`
      app_id,
      app_name,
      rating,
      review_count,
      pricing_plans (
        plan_id,
        plan_name,
        monthly_price,
        yearly_price,
        is_free,
        has_trial
      )
    `);

  if (error) throw new Error(error.message);
  return data;
}

export async function fetchHistoricalCompetitors(user_id: string, app_ids: (string | number)[]): Promise<Pick<App, 'app_id' | 'review_count' | 'rating'>[] | null> {
  const logger = await getLogger();
  const ctx = { name: 'fetchHistoricalCompetitors', user_id, app_ids_count: app_ids.length };

  logger.info(ctx, 'Fetching historical competitor data');

  // **IMPORTANT:** Replace 'competitor_historical_data' with the actual name of your historical data table.
  const { data, error } = await supabase
    .from('competitor_shopify_apps') // <-- Using competitor_shopify_apps as per user
    .select('app_id, review_count, rating, url') // Also selecting URL for potential verification
    .in('app_id', app_ids.map(String)) // Ensure app_ids are strings if your table uses text/varchar for app_id
    // For this comparison approach, we might not need complex date filtering here
    // .gte('created_at', sevenDaysAgo.toISOString()) // Filter for records in the last 7 days
    .order('created_at', { ascending: false }); // Get most recent first if multiple exist

  if (error) {
    logger.error({ ...ctx, error: error.message }, 'Error fetching historical competitor data from competitor_shopify_apps');
    return null;
  }

  logger.info({ ...ctx, count: data?.length || 0 }, 'Successfully fetched historical competitor data');
  // Simple approach: just return the fetched data. The comparison logic will be in the API route.
  return data as Pick<App, 'app_id' | 'review_count' | 'rating'>[] | null;
}

// New function to fetch review counts and ratings from both tables for comparison using app_url
export async function fetchReviewCountsAndRatingsForComparison(appUrl: string): Promise<{
  currentReviewCount: number | null;
  competitorReviewCount: number | null;
  currentRating: number | null;
  competitorRating: number | null;
} | null> {
  const logger = await getLogger();
  const ctx = { name: 'fetchReviewCountsAndRatingsForComparison', appUrl };

  logger.info(ctx, 'Fetching review counts and ratings for comparison');

  try {
    // Fetch current review count and rating from shopify_apps using app_url
    const { data: currentAppData, error: currentError } = await supabase
      .from('shopify_apps')
      .select('review_count, rating')
      .eq('url', appUrl)
      .single();

    if (currentError) {
      logger.error({ ...ctx, error: currentError.message }, 'Error fetching current review count and rating from shopify_apps');
      // Continue to fetch from competitor_shopify_apps even if current fetch fails
    }

    // Fetch competitor review count and rating from competitor_shopify_apps using app_url
    const { data: competitorAppData, error: competitorError } = await supabase
      .from('competitor_shopify_apps')
      .select('review_count, rating')
      .eq('url', appUrl)
      .single();

    if (competitorError) {
      logger.error({ ...ctx, error: competitorError.message }, 'Error fetching competitor review count and rating from competitor_shopify_apps');
      // Continue even if competitor fetch fails
    }

    // If both fetches failed or found nothing, return null
    if (!currentAppData && !competitorAppData) {
      logger.warn(ctx, 'No review data found in either table for url');
      return null;
    }

    logger.info({
      ...ctx,
      currentData: !!currentAppData,
      competitorData: !!competitorAppData
    }, 'Successfully fetched review comparison data');

    return {
      currentReviewCount: currentAppData?.review_count ?? null,
      competitorReviewCount: competitorAppData?.review_count ?? null,
      currentRating: currentAppData?.rating ?? null,
      competitorRating: competitorAppData?.rating ?? null,
    };

  } catch (error) {
    logger.error({ ...ctx, error }, 'Unexpected error in fetchReviewCountsAndRatingsForComparison');
    return null;
  }
}

export async function fetchMarketLeadersByCategoryFeature(categoryFeatureHeading: string = 'SEO'): Promise<(App & { categories: string[]; marketShare?: number })[] | null> {
  // First get all apps with this category feature
  const apps = await fetchAppsByCategoryFeature(categoryFeatureHeading);
  
  if (!apps || apps.length === 0) {
    return null;
  }

  // Calculate total reviews for market share calculation
  const totalReviews = apps.reduce((sum, app) => sum + (Number(app.review_count) || 0), 0);

  const appsWithLeadershipScores = apps.map(app => {
    const appReviewCount = Number(app.review_count) || 0;
    const appRating = app.rating || 0;

    // 1. Basic Market Share (Review-Based)
    const reviewMarketShare = totalReviews > 0 ? (appReviewCount / totalReviews) * 100 : 0;

    // 2. Quality Score (0-1 scale)
    // Simplified as individual star counts are not available.
    const qualityScore = (appRating - 1) / 4; // Converts 1-5 rating to 0-1 scale

    // 3. Market Leader Score (Simple Leadership Formula)
    // Review_Market_Share normalized to 0-1 (divide by 100)
    const leadershipScore = (reviewMarketShare / 100 * 0.6) + (qualityScore * 0.4);

    return {
      ...app,
      marketShare: reviewMarketShare,
      qualityScore: qualityScore,
      leadershipScore: leadershipScore,
    };
  });

  // Sort apps by Leadership Score
  const sortedApps = appsWithLeadershipScores
    .sort((a, b) => (b.leadershipScore || 0) - (a.leadershipScore || 0))
    // .slice(0, 10); // Display only top 10 apps

  // 4. Leader Classification (Optional: can be done in the frontend if needed for display)
  // This classification logic can be added here or in the component that renders the leaders.
  // For now, I'm calculating the scores and sorting.

  return sortedApps;
}

