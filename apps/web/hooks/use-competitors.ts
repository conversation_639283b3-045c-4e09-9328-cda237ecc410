import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { queryKeys, queryOptions } from '~/lib/react-query/client';
import { Competitor, App, PricingPlan } from '~/app/types/data';
import { isSuccess } from '~/lib/errors';

/**
 * Fetch competitors data from API
 */
async function fetchCompetitors(): Promise<{
  competitors: Competitor[];
  apps: (App & { categories: string[] })[];
  pricingPlans: PricingPlan[];
  marketShares: Array<{ name: string; share: string; reviewCount: number }>;
  trends: Array<{ name: string; trend: 'up' | 'down' | 'stable'; change: number }>;
  alerts: Array<{ type: 'rating' | 'reviews' | 'pricing'; message: string; severity: 'low' | 'medium' | 'high' }>;
}> {
  const response = await fetch('/api/competitors');
  
  if (!response.ok) {
    throw new Error(`Failed to fetch competitors: ${response.status} ${response.statusText}`);
  }
  
  return response.json();
}

/**
 * Add a new competitor
 */
async function addCompetitor(appUrl: string): Promise<{ app_id: number }> {
  const response = await fetch('/api/competitors', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ app_url: appUrl }),
  });
  
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || `Failed to add competitor: ${response.status}`);
  }
  
  const result = await response.json();
  return result.data;
}

/**
 * Delete a competitor
 */
async function deleteCompetitor(id: string): Promise<void> {
  const response = await fetch('/api/competitors', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ id }),
  });
  
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || `Failed to delete competitor: ${response.status}`);
  }
}

/**
 * Hook to fetch competitors data
 */
export function useCompetitors() {
  return useQuery({
    queryKey: queryKeys.competitors.all,
    queryFn: fetchCompetitors,
    ...queryOptions.userSpecific,
    select: (data) => ({
      ...data,
      // Add computed properties
      totalCompetitors: data.competitors.length,
      avgRating: data.apps.length > 0 
        ? data.apps.reduce((sum, app) => sum + (Number(app.rating) || 0), 0) / data.apps.length 
        : 0,
      avgReviews: data.apps.length > 0 
        ? data.apps.reduce((sum, app) => sum + (Number(app.review_count) || 0), 0) / data.apps.length 
        : 0,
      topRated: data.apps.reduce((prev, curr) => 
        (Number(curr.rating) || 0) > (Number(prev.rating) || 0) ? curr : prev, 
        data.apps[0]
      ),
    }),
  });
}

/**
 * Hook to add a competitor
 */
export function useAddCompetitor() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: addCompetitor,
    onSuccess: () => {
      // Invalidate and refetch competitors data
      queryClient.invalidateQueries({ queryKey: queryKeys.competitors.all });
      // Also invalidate related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.apps.all });
      queryClient.invalidateQueries({ queryKey: queryKeys.pricingPlans.all });
    },
    onError: (error) => {
      console.error('Failed to add competitor:', error);
    },
  });
}

/**
 * Hook to delete a competitor
 */
export function useDeleteCompetitor() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: deleteCompetitor,
    onSuccess: () => {
      // Invalidate and refetch competitors data
      queryClient.invalidateQueries({ queryKey: queryKeys.competitors.all });
    },
    onError: (error) => {
      console.error('Failed to delete competitor:', error);
    },
  });
}

/**
 * Hook to get competitors loading state
 */
export function useCompetitorsStatus() {
  const query = useCompetitors();
  
  return {
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    isSuccess: query.isSuccess,
    isFetching: query.isFetching,
    isRefetching: query.isRefetching,
  };
}
