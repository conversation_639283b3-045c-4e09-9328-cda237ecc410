import { useQuery } from '@tanstack/react-query';
import { queryKeys, queryOptions } from '~/lib/react-query/client';
import { App, PricingPlan } from '~/app/types/data';

export interface InsightData extends App {
  categories: string[];
  pricing_plans: PricingPlan[];
}

/**
 * Fetch insights data from API
 */
async function fetchInsights(category?: string): Promise<InsightData[]> {
  const url = category 
    ? `/api/insights?category=${encodeURIComponent(category)}`
    : '/api/insights';
    
  const response = await fetch(url);
  
  if (!response.ok) {
    throw new Error(`Failed to fetch insights: ${response.status} ${response.statusText}`);
  }
  
  return response.json();
}

/**
 * Fetch market leaders data
 */
async function fetchMarketLeaders(category?: string): Promise<Array<{
  app_id: number;
  app_name: string;
  rating: number;
  review_count: number;
  marketShare: number;
  categories: string[];
}>> {
  const url = category 
    ? `/api/market-leaders?category=${encodeURIComponent(category)}`
    : '/api/market-leaders';
    
  const response = await fetch(url);
  
  if (!response.ok) {
    throw new Error(`Failed to fetch market leaders: ${response.status} ${response.statusText}`);
  }
  
  return response.json();
}

/**
 * Hook to fetch insights data
 */
export function useInsights(category?: string) {
  return useQuery({
    queryKey: category 
      ? queryKeys.insights.byCategory(category)
      : queryKeys.insights.all,
    queryFn: () => fetchInsights(category),
    ...queryOptions.userSpecific,
    select: (data) => ({
      insights: data,
      totalApps: data.length,
      avgRating: data.length > 0 
        ? data.reduce((sum, app) => sum + (Number(app.rating) || 0), 0) / data.length 
        : 0,
      avgReviews: data.length > 0 
        ? data.reduce((sum, app) => sum + (Number(app.review_count) || 0), 0) / data.length 
        : 0,
      categories: Array.from(new Set(data.flatMap(app => app.categories))),
      priceRanges: {
        free: data.filter(app => app.pricing_plans?.some(plan => plan.is_free)).length,
        paid: data.filter(app => app.pricing_plans?.some(plan => !plan.is_free)).length,
      },
    }),
  });
}

/**
 * Hook to fetch market leaders
 */
export function useMarketLeaders(category?: string) {
  return useQuery({
    queryKey: category 
      ? queryKeys.marketLeaders.byCategory(category)
      : queryKeys.marketLeaders.all,
    queryFn: () => fetchMarketLeaders(category),
    ...queryOptions.userSpecific,
    select: (data) => ({
      leaders: data,
      totalMarketShare: data.reduce((sum, leader) => sum + leader.marketShare, 0),
      topLeader: data[0], // Assuming data is sorted by market share
      competitionLevel: data.length > 10 ? 'high' : data.length > 5 ? 'medium' : 'low',
    }),
  });
}

/**
 * Hook to get insights loading state
 */
export function useInsightsStatus(category?: string) {
  const query = useInsights(category);
  
  return {
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    isSuccess: query.isSuccess,
    isFetching: query.isFetching,
    isRefetching: query.isRefetching,
  };
}

/**
 * Hook to get market leaders loading state
 */
export function useMarketLeadersStatus(category?: string) {
  const query = useMarketLeaders(category);
  
  return {
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    isSuccess: query.isSuccess,
    isFetching: query.isFetching,
    isRefetching: query.isRefetching,
  };
}

/**
 * Combined hook for category analysis
 */
export function useCategoryAnalysis(category: string) {
  const insights = useInsights(category);
  const marketLeaders = useMarketLeaders(category);
  
  return {
    insights: insights.data,
    marketLeaders: marketLeaders.data,
    isLoading: insights.isLoading || marketLeaders.isLoading,
    isError: insights.isError || marketLeaders.isError,
    error: insights.error || marketLeaders.error,
    isSuccess: insights.isSuccess && marketLeaders.isSuccess,
    analysis: insights.data && marketLeaders.data ? {
      totalApps: insights.data.totalApps,
      marketConcentration: marketLeaders.data.leaders.slice(0, 5).reduce((sum, leader) => sum + leader.marketShare, 0),
      avgRating: insights.data.avgRating,
      competitionLevel: marketLeaders.data.competitionLevel,
      opportunities: {
        lowRatedApps: insights.data.insights.filter(app => (app.rating || 0) < 4.0).length,
        freeApps: insights.data.priceRanges.free,
        paidApps: insights.data.priceRanges.paid,
      },
    } : undefined,
  };
}
