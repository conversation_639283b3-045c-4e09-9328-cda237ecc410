import { BigQuery } from '@google-cloud/bigquery';
import { getLogger } from '@kit/shared/logger';

let bigqueryClient: BigQuery | null = null;
    
export async function getBigQueryClient(): Promise<BigQuery> {
  const logger = await getLogger();
  const ctx = { name: 'bigquery.getClient' };

  if (bigqueryClient) {
    return bigqueryClient;
  }

  try {
    if (!process.env.GOOGLE_CLOUD_CREDENTIALS) {
      throw new Error('Missing GOOGLE_APPLICATION_CREDENTIALS environment variable');
    }

    bigqueryClient = new BigQuery({
      keyFilename: process.env.GOOGLE_CLOUD_CREDENTIALS,
      projectId: process.env.GOOGLE_CLOUD_PROJECT
    });

    logger.info(ctx, 'BigQuery client initialized successfully');
    return bigqueryClient;
  } catch (error: unknown) {
    logger.error({ ...ctx, error }, 'Failed to initialize BigQuery client');
    throw new Error('Failed to initialize BigQuery client');
  }
} 