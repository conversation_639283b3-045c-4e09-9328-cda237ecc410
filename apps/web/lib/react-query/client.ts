import { QueryClient } from '@tanstack/react-query';

/**
 * Create a new QueryClient instance with optimized defaults
 */
export function createQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // Stale time: 5 minutes - data is considered fresh for 5 minutes
        staleTime: 5 * 60 * 1000,
        // Cache time: 10 minutes - data stays in cache for 10 minutes after becoming unused
        gcTime: 10 * 60 * 1000,
        // Retry failed requests 3 times with exponential backoff
        retry: (failureCount, error: any) => {
          // Don't retry on 4xx errors (client errors)
          if (error?.status >= 400 && error?.status < 500) {
            return false;
          }
          // Retry up to 3 times for other errors
          return failureCount < 3;
        },
        // Retry delay with exponential backoff
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
        // Refetch on window focus for critical data
        refetchOnWindowFocus: true,
        // Don't refetch on reconnect by default (can be overridden per query)
        refetchOnReconnect: 'always',
      },
      mutations: {
        // Retry mutations once
        retry: 1,
        // Retry delay for mutations
        retryDelay: 1000,
      },
    },
  });
}

/**
 * Default query client instance
 */
export const queryClient = createQueryClient();

/**
 * Query keys factory for consistent key management
 */
export const queryKeys = {
  // Competitors
  competitors: {
    all: ['competitors'] as const,
    byUser: (userId: string) => ['competitors', 'user', userId] as const,
    detail: (id: string) => ['competitors', 'detail', id] as const,
  },
  
  // Apps
  apps: {
    all: ['apps'] as const,
    byIds: (ids: number[]) => ['apps', 'byIds', ids.sort()] as const,
    byCategory: (category: string) => ['apps', 'category', category] as const,
    detail: (id: number) => ['apps', 'detail', id] as const,
  },
  
  // Pricing Plans
  pricingPlans: {
    all: ['pricingPlans'] as const,
    byAppIds: (appIds: number[]) => ['pricingPlans', 'byAppIds', appIds.sort()] as const,
  },
  
  // Screenshots
  screenshots: {
    all: ['screenshots'] as const,
    byAppIds: (appIds: number[]) => ['screenshots', 'byAppIds', appIds.sort()] as const,
  },
  
  // Market Leaders
  marketLeaders: {
    all: ['marketLeaders'] as const,
    byCategory: (category: string) => ['marketLeaders', 'category', category] as const,
  },
  
  // Insights
  insights: {
    all: ['insights'] as const,
    byCategory: (category: string) => ['insights', 'category', category] as const,
  },
  
  // Historical Data
  historical: {
    competitors: (userId: string, appIds: (string | number)[]) => 
      ['historical', 'competitors', userId, appIds.sort()] as const,
    reviewComparison: (appUrl: string) => 
      ['historical', 'reviewComparison', appUrl] as const,
  },
} as const;

/**
 * Common query options
 */
export const queryOptions = {
  // For data that changes frequently
  realtime: {
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 60 * 1000, // Refetch every minute
  },
  
  // For data that rarely changes
  static: {
    staleTime: 60 * 60 * 1000, // 1 hour
    gcTime: 24 * 60 * 60 * 1000, // 24 hours
    refetchOnWindowFocus: false,
  },
  
  // For user-specific data
  userSpecific: {
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: true,
  },
} as const;
