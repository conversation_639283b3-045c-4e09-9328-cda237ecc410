/**
 * Custom error types for better error handling throughout the application
 */

export class DatabaseError extends <PERSON><PERSON>r {
  constructor(
    message: string,
    public readonly operation: string,
    public readonly table?: string,
    public readonly originalError?: unknown
  ) {
    super(message);
    this.name = 'DatabaseError';
  }
}

export class ValidationError extends <PERSON>rror {
  constructor(
    message: string,
    public readonly field?: string,
    public readonly value?: unknown
  ) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class NotFoundError extends Error {
  constructor(
    message: string,
    public readonly resource: string,
    public readonly identifier?: string | number
  ) {
    super(message);
    this.name = 'NotFoundError';
  }
}

export class UnauthorizedError extends Error {
  constructor(message: string = 'Unauthorized access') {
    super(message);
    this.name = 'UnauthorizedError';
  }
}

export class RateLimitError extends Error {
  constructor(
    message: string = 'Rate limit exceeded',
    public readonly retryAfter?: number
  ) {
    super(message);
    this.name = 'RateLimitError';
  }
}

/**
 * Result type for operations that can fail
 */
export type Result<T, E = Error> = 
  | { success: true; data: T }
  | { success: false; error: E };

/**
 * Helper function to create success result
 */
export function success<T>(data: T): Result<T, never> {
  return { success: true, data };
}

/**
 * Helper function to create error result
 */
export function failure<E extends Error>(error: E): Result<never, E> {
  return { success: false, error };
}

/**
 * Type guard to check if result is successful
 */
export function isSuccess<T, E>(result: Result<T, E>): result is { success: true; data: T } {
  return result.success;
}

/**
 * Type guard to check if result is failure
 */
export function isFailure<T, E>(result: Result<T, E>): result is { success: false; error: E } {
  return !result.success;
}

/**
 * Safely execute an async operation and return a Result
 */
export async function safeAsync<T>(
  operation: () => Promise<T>,
  errorMessage?: string
): Promise<Result<T, Error>> {
  try {
    const data = await operation();
    return success(data);
  } catch (error) {
    const errorObj = error instanceof Error ? error : new Error(errorMessage || 'Unknown error');
    return failure(errorObj);
  }
}

/**
 * Safely execute a sync operation and return a Result
 */
export function safe<T>(
  operation: () => T,
  errorMessage?: string
): Result<T, Error> {
  try {
    const data = operation();
    return success(data);
  } catch (error) {
    const errorObj = error instanceof Error ? error : new Error(errorMessage || 'Unknown error');
    return failure(errorObj);
  }
}
