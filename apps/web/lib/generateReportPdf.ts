import puppeteer from 'puppeteer';

export async function generateReportPdf(html: string): Promise<Buffer> {
  let browser;
  try {
    // @ts-ignore
    browser = await puppeteer.launch({ headless: 'new' }); // Use 'new' for the new headless mode
    const page = await browser.newPage();
    await page.setContent(html, { waitUntil: 'networkidle0' }); // Wait until network is idle
    
    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true, // Ensure background colors/images are printed
    });

    return Buffer.from(pdfBuffer);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
} 