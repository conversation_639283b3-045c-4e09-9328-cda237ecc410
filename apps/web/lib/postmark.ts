import { Client } from 'postmark';

const client = new Client(process.env.POSTMARK_API_KEY!);

export async function sendWelcomeEmail(to: string, name: string) {
  return await client.sendEmail({
    From: process.env.POSTMARK_FROM_EMAIL!,
    To: to,
    Subject: 'Welcome to Our App!',
    HtmlBody: `<strong>Hi ${name},</strong><br/>Thanks for joining us!`,
    TextBody: `Hi ${name},\nThanks for joining us!`,
    MessageStream: 'outbound', // default stream
  });
}
