import { PDFDocument, rgb, StandardFonts } from 'pdf-lib';

interface CategoryMarketShareData {
  name: string;
  leader: string;
  marketShare: string;
  averageGrowth: string;
}

export async function generateMarketShareReportPdf(data: CategoryMarketShareData[]): Promise<Buffer> {
  const pdfDoc = await PDFDocument.create();
  const page = pdfDoc.addPage();
  const { width, height } = page.getSize();
  const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
  const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

  const fontSize = 12;
  const margin = 50;
  let yPosition = height - margin;

  // Add Title
  page.drawText('Weekly Market Share Report', {
    x: margin,
    y: yPosition,
    font,
    size: 18,
    color: rgb(0, 0, 0),
  });

  yPosition -= 30;

  // Add Table Headers
  const headers = ['Category', 'Leader', 'Market Share', 'Avg Growth'];
  const columnWidths = [150, 150, 80, 80];
  let xPosition = margin;

  headers.forEach((header, index) => {
    page.drawText(header, {
      x: xPosition,
      y: yPosition,
      font: boldFont,
      size: fontSize,
      color: rgb(0, 0, 0),
    });
    xPosition += columnWidths[index]!;
  });

  yPosition -= 20;

  // Add Table Data
  data.forEach(item => {
    xPosition = margin;
    const rowData = [item.name, item.leader, item.marketShare, item.averageGrowth || 'N/A'];

    rowData.forEach((cellData, index) => {
      page.drawText(cellData,
        {
          x: xPosition,
          y: yPosition,
          font,
          size: fontSize,
          color: rgb(0, 0, 0),
        }
      );
      xPosition += columnWidths[index]!;
    });
    yPosition -= 15; // Move down for the next row
  });

  // Serialize the PDF to bytes
  const pdfBytes = await pdfDoc.save();

  // Convert bytes to Buffer and return
  return Buffer.from(pdfBytes);
} 