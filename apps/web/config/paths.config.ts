import { z } from 'zod';

const PathsSchema = z.object({
  auth: z.object({
    signIn: z.string().min(1),
    signUp: z.string().min(1),
    verifyMfa: z.string().min(1),
    callback: z.string().min(1),
    passwordReset: z.string().min(1),
    passwordUpdate: z.string().min(1),
  }),
  app: z.object({
    home: z.string().min(1),
    personalAccountSettings: z.string().min(1),
    personalAccountBilling: z.string().min(1),
    personalAccountBillingReturn: z.string().min(1),
    accountHome: z.string().min(1),
    accountSettings: z.string().min(1),
    accountBilling: z.string().min(1),
    accountMembers: z.string().min(1),
    accountBillingReturn: z.string().min(1),
    joinTeam: z.string().min(1),
    insights: z.string().min(1),
    competitor: z.string().min(1),
    alerts: z.string().min(1),
    category: z.string().min(1),
    reports: z.string().min(1),
  }),
});

const pathsConfig = PathsSchema.parse({
  auth: {
    signIn: '/auth/sign-in',
    signUp: '/auth/sign-up',
    verifyMfa: '/auth/verify',
    callback: '/auth/callback',
    passwordReset: '/auth/password-reset',
    passwordUpdate: '/update-password',
  },
  app: {
    home: '/home',
    personalAccountSettings: '/home/<USER>',
    personalAccountBilling: '/home/<USER>',
    personalAccountBillingReturn: '/home/<USER>/return',
    accountHome: '/home/<USER>',
    accountSettings: `/home/<USER>/settings`,
    accountBilling: `/home/<USER>/billing`,
    accountMembers: `/home/<USER>/members`,
    accountBillingReturn: `/home/<USER>/billing/return`,
    joinTeam: '/join',
    insights: '/home/<USER>',
    competitor: '/home/<USER>',
    alerts:'/home/<USER>',
    category: '/home/<USER>',
    reports: '/home/<USER>'
  },
} satisfies z.infer<typeof PathsSchema>);

export default pathsConfig;
