import { CreditCard, Home, User, Bell, FileText } from 'lucide-react';
import { z } from 'zod';

import { NavigationConfigSchema } from '@kit/ui/navigation-schema';

import featureFlagsConfig from '~/config/feature-flags.config';
import pathsConfig from '~/config/paths.config';

const iconClasses = 'w-4';

const routes = [
  {
    label: 'common:routes.application',
    children: [
      {
        label: 'common:routes.home',
        path: pathsConfig.app.home,
        Icon: <Home className={iconClasses} />,
        end: true,
      },
      {
        label: 'common:routes.alerts',
        path: pathsConfig.app.alerts,
        Icon: <Bell className={iconClasses} />,
        end: true,
      },
    ],

  },
  {
    label: 'common:routes.measure_heading',
    children: [
      {
        label: 'common:routes.insights',
        path: pathsConfig.app.insights,
        Icon: <Home className={iconClasses} />,
        end: true,
      },
    ],
  },
 
  {
    label: 'common:routes.track_heading',
    children: [
      {
        label: 'common:routes.competitor',
        path: pathsConfig.app.competitor,
        Icon: <Home className={iconClasses} />,
        end: true,
      },
      {
        label: 'Category',
        path: pathsConfig.app.category,
        Icon: <Home className={iconClasses} />,
        end: true,
      },
    ],
  },
  {
    label: 'Reports',
    children: [
      {
        label: 'Category Reports',
        path: pathsConfig.app.reports,
        Icon: <FileText className={iconClasses} />,
        end: true,
      },
    ],
  },
  {
    label: 'common:routes.settings',
    children: [
      {
        label: 'common:routes.profile',
        path: pathsConfig.app.personalAccountSettings,
        Icon: <User className={iconClasses} />,
      },
      featureFlagsConfig.enablePersonalAccountBilling
        ? {
            label: 'common:routes.billing',
            path: pathsConfig.app.personalAccountBilling,
            Icon: <CreditCard className={iconClasses} />,
          }
        : undefined,
    ].filter((route) => !!route),
  },
] satisfies z.infer<typeof NavigationConfigSchema>['routes'];

export const personalAccountNavigationConfig = NavigationConfigSchema.parse({
  routes,
  style: process.env.NEXT_PUBLIC_USER_NAVIGATION_STYLE,
  sidebarCollapsed: process.env.NEXT_PUBLIC_HOME_SIDEBAR_COLLAPSED,
});
