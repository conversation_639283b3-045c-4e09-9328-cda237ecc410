{"extends": "@kit/tsconfig/base.json", "compilerOptions": {"baseUrl": ".", "paths": {"~/*": ["./app/*"], "~/config/*": ["./config/*"], "~/components/*": ["./components/*"], "~/lib/*": ["./lib/*"], "~/styles/*": ["./styles/*"]}, "plugins": [{"name": "next"}], "tsBuildInfoFile": "node_modules/.cache/tsbuildinfo.json"}, "include": ["next-env.d.ts", ".next/types/**/*.ts", "*.ts", "*.tsx", "*.mjs", "./config/**/*.ts", "components/**/*", "lib/**/*.ts", "app"], "exclude": ["node_modules", ".next"]}