{"name": "@kit/tailwind-config", "version": "0.1.0", "main": "index.ts", "license": "MIT", "files": ["index.ts", "postcss.js"], "scripts": {"clean": "rm -rf .turbo node_modules", "lint": "eslint .", "format": "prettier --check \"**/*.{js,ts,json}\"", "typecheck": "tsc --noEmit"}, "dependencies": {"autoprefixer": "^10.4.20", "postcss": "8.4.49", "tailwindcss": "3.4.16", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tsconfig": "workspace:*", "eslint": "^8.57.0", "prettier": "^3.4.2", "typescript": "^5.7.2"}, "eslintConfig": {"root": true, "extends": ["@kit/eslint-config/base"]}, "prettier": "@kit/prettier-config"}