{"name": "@kit/prettier-config", "private": true, "version": "0.1.0", "main": "index.mjs", "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check \"**/*.{mjs,json}\"", "typecheck": "tsc --noEmit"}, "dependencies": {"@trivago/prettier-plugin-sort-imports": "4.3.0", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9"}, "devDependencies": {"@kit/tsconfig": "workspace:*", "typescript": "^5.7.2"}, "prettier": "./index.mjs"}